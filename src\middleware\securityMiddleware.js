require('dotenv').config();

const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const validator = require('validator');
const chalk = require('chalk');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

const env = process.env.HOST_ENV || 'development';
const config = require('../config/envConfig')[env];

// Enhanced security headers
const securityHeaders = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
});

// Rate limiting configuration
const createRateLimiter = (windowMs, max, message) => {
    return rateLimit({
        windowMs,
        max,
        message: { error: message },
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
            console.warn(chalk.yellow(`Rate limit exceeded for IP: ${req.ip}`));
            res.status(429).json({ error: message });
        }
    });
};

// Different rate limits for different endpoints
const generalRateLimit = createRateLimiter(
    15 * 60 * 1000, // 15 minutes
    100, // limit each IP to 100 requests per windowMs
    'Too many requests from this IP, please try again later.'
);

const authRateLimit = createRateLimiter(
    15 * 60 * 1000, // 15 minutes
    5, // limit each IP to 5 auth requests per windowMs
    'Too many authentication attempts, please try again later.'
);

const apiRateLimit = createRateLimiter(
    1 * 60 * 1000, // 1 minute
    30, // limit each IP to 30 API requests per minute
    'API rate limit exceeded, please slow down.'
);

// Input validation middleware
const validateInput = (schema) => {
    return (req, res, next) => {
        try {
            const { error, value } = schema.validate(req.body, { 
                abortEarly: false,
                stripUnknown: true 
            });
            
            if (error) {
                const errors = error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message
                }));
                
                console.warn(chalk.yellow(`Validation error for IP: ${req.ip}`, errors));
                return res.status(400).json({ 
                    error: 'Validation failed', 
                    details: errors 
                });
            }
            
            req.body = value;
            next();
        } catch (err) {
            console.error(chalk.red('Validation middleware error:'), err);
            res.status(500).json({ error: 'Internal validation error' });
        }
    };
};

// Enhanced JWT authentication
const generateJWT = (payload, expiresIn = '1h') => {
    const secret = process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex');
    return jwt.sign(payload, secret, { 
        expiresIn,
        issuer: 'footprints-e-invoice',
        audience: 'gsp-api'
    });
};

const verifyJWT = (token) => {
    const secret = process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex');
    return jwt.verify(token, secret);
};

// Enhanced authentication middleware
const enhancedAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ 
                error: 'Unauthorized: Bearer token required' 
            });
        }
        
        const token = authHeader.substring(7);
        
        try {
            const decoded = verifyJWT(token);
            req.user = decoded;
            
            // Log successful authentication
            console.log(chalk.green(`Authenticated user: ${decoded.email || 'unknown'}`));
            
            next();
        } catch (jwtError) {
            console.warn(chalk.yellow(`Invalid JWT token from IP: ${req.ip}`));
            return res.status(401).json({ 
                error: 'Unauthorized: Invalid or expired token' 
            });
        }
        
    } catch (error) {
        console.error(chalk.red('Authentication error:'), error);
        res.status(500).json({ error: 'Internal authentication error' });
    }
};

// Request sanitization
const sanitizeInput = (req, res, next) => {
    try {
        // Sanitize query parameters
        for (const key in req.query) {
            if (typeof req.query[key] === 'string') {
                req.query[key] = validator.escape(req.query[key]);
            }
        }
        
        // Sanitize body parameters (for string values)
        const sanitizeObject = (obj) => {
            for (const key in obj) {
                if (typeof obj[key] === 'string') {
                    obj[key] = validator.escape(obj[key]);
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    sanitizeObject(obj[key]);
                }
            }
        };
        
        if (req.body && typeof req.body === 'object') {
            sanitizeObject(req.body);
        }
        
        next();
    } catch (error) {
        console.error(chalk.red('Input sanitization error:'), error);
        res.status(500).json({ error: 'Input processing error' });
    }
};

// Security logging middleware
const securityLogger = (req, res, next) => {
    const startTime = Date.now();
    
    // Log request details
    console.log(chalk.blue(`[${new Date().toISOString()}] ${req.method} ${req.path} - IP: ${req.ip}`));
    
    // Override res.json to log responses
    const originalJson = res.json;
    res.json = function(data) {
        const duration = Date.now() - startTime;
        
        if (res.statusCode >= 400) {
            console.warn(chalk.yellow(`[${new Date().toISOString()}] ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms - IP: ${req.ip}`));
        } else {
            console.log(chalk.green(`[${new Date().toISOString()}] ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms - IP: ${req.ip}`));
        }
        
        return originalJson.call(this, data);
    };
    
    next();
};

// CSRF protection for state-changing operations
const csrfProtection = (req, res, next) => {
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method)) {
        const csrfToken = req.headers['x-csrf-token'];
        const sessionToken = req.session?.csrfToken;
        
        if (!csrfToken || csrfToken !== sessionToken) {
            console.warn(chalk.yellow(`CSRF token mismatch for IP: ${req.ip}`));
            return res.status(403).json({ error: 'CSRF token validation failed' });
        }
    }
    next();
};

module.exports = {
    securityHeaders,
    generalRateLimit,
    authRateLimit,
    apiRateLimit,
    validateInput,
    enhancedAuth,
    sanitizeInput,
    securityLogger,
    csrfProtection,
    generateJWT,
    verifyJWT
};
