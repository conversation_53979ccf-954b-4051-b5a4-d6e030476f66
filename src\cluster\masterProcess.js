require('dotenv').config();

const cluster = require('cluster');
const os = require('os');
const chalk = require('chalk');
const path = require('path');

const env = process.env.HOST_ENV || 'development';
const config = require('../config/envConfig')[env];

class MasterProcess {
    constructor() {
        this.workers = new Map();
        this.isShuttingDown = false;
        this.maxWorkers = process.env.MAX_WORKERS || os.cpus().length;
        this.workerRestartDelay = 5000; // 5 seconds
        this.maxRestarts = 5;
        this.restartCounts = new Map();
        
        this.setupCluster();
        this.setupSignalHandlers();
        this.startHealthMonitoring();
    }

    setupCluster() {
        cluster.setupPrimary({
            exec: path.join(__dirname, '../../server.js'),
            silent: false
        });

        console.log(chalk.blue(`[Master] Starting cluster with ${this.maxWorkers} workers`));
        console.log(chalk.blue(`[Master] Process ID: ${process.pid}`));
        console.log(chalk.blue(`[Master] Environment: ${env}`));

        // Fork workers
        for (let i = 0; i < this.maxWorkers; i++) {
            this.forkWorker();
        }

        // Handle worker events
        cluster.on('fork', (worker) => {
            console.log(chalk.green(`[Master] Worker ${worker.process.pid} forked`));
            this.workers.set(worker.id, {
                worker,
                startTime: Date.now(),
                restarts: this.restartCounts.get(worker.id) || 0
            });
        });

        cluster.on('online', (worker) => {
            console.log(chalk.green(`[Master] Worker ${worker.process.pid} is online`));
        });

        cluster.on('listening', (worker, address) => {
            console.log(chalk.green(`[Master] Worker ${worker.process.pid} listening on ${address.address}:${address.port}`));
        });

        cluster.on('disconnect', (worker) => {
            console.log(chalk.yellow(`[Master] Worker ${worker.process.pid} disconnected`));
        });

        cluster.on('exit', (worker, code, signal) => {
            console.log(chalk.red(`[Master] Worker ${worker.process.pid} died (${signal || code})`));
            this.handleWorkerExit(worker, code, signal);
        });
    }

    forkWorker() {
        if (this.isShuttingDown) {
            return null;
        }

        const worker = cluster.fork();
        
        // Set up worker message handling
        worker.on('message', (message) => {
            this.handleWorkerMessage(worker, message);
        });

        return worker;
    }

    handleWorkerExit(worker, code, signal) {
        this.workers.delete(worker.id);

        if (this.isShuttingDown) {
            console.log(chalk.blue(`[Master] Worker ${worker.process.pid} exited during shutdown`));
            return;
        }

        const restartCount = this.restartCounts.get(worker.id) || 0;
        
        if (restartCount >= this.maxRestarts) {
            console.error(chalk.red(`[Master] Worker ${worker.id} exceeded max restarts (${this.maxRestarts}). Not restarting.`));
            return;
        }

        // Increment restart count
        this.restartCounts.set(worker.id, restartCount + 1);

        console.log(chalk.yellow(`[Master] Restarting worker ${worker.id} in ${this.workerRestartDelay}ms (attempt ${restartCount + 1}/${this.maxRestarts})`));
        
        setTimeout(() => {
            if (!this.isShuttingDown) {
                this.forkWorker();
            }
        }, this.workerRestartDelay);
    }

    handleWorkerMessage(worker, message) {
        switch (message.type) {
            case 'health-check':
                worker.send({ type: 'health-response', timestamp: Date.now() });
                break;
            
            case 'worker-ready':
                console.log(chalk.green(`[Master] Worker ${worker.process.pid} is ready`));
                break;
            
            case 'worker-error':
                console.error(chalk.red(`[Master] Worker ${worker.process.pid} reported error:`, message.error));
                break;
            
            case 'graceful-shutdown':
                console.log(chalk.yellow(`[Master] Worker ${worker.process.pid} requesting graceful shutdown`));
                this.gracefulShutdown();
                break;
            
            default:
                console.log(chalk.blue(`[Master] Received message from worker ${worker.process.pid}:`, message));
        }
    }

    setupSignalHandlers() {
        // Graceful shutdown on SIGTERM
        process.on('SIGTERM', () => {
            console.log(chalk.yellow('[Master] Received SIGTERM, initiating graceful shutdown...'));
            this.gracefulShutdown();
        });

        // Graceful shutdown on SIGINT (Ctrl+C)
        process.on('SIGINT', () => {
            console.log(chalk.yellow('[Master] Received SIGINT, initiating graceful shutdown...'));
            this.gracefulShutdown();
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error(chalk.red('[Master] Uncaught Exception:'), error);
            this.emergencyShutdown();
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            console.error(chalk.red('[Master] Unhandled Rejection at:'), promise, 'reason:', reason);
            this.emergencyShutdown();
        });
    }

    startHealthMonitoring() {
        const healthCheckInterval = 30000; // 30 seconds
        
        setInterval(() => {
            if (this.isShuttingDown) return;
            
            console.log(chalk.blue(`[Master] Health check - Active workers: ${Object.keys(cluster.workers).length}`));
            
            // Check if we need to restart any workers
            const activeWorkers = Object.keys(cluster.workers).length;
            if (activeWorkers < this.maxWorkers && !this.isShuttingDown) {
                console.log(chalk.yellow(`[Master] Worker count below target (${activeWorkers}/${this.maxWorkers}), forking new worker`));
                this.forkWorker();
            }
            
            // Send health check to all workers
            for (const id in cluster.workers) {
                const worker = cluster.workers[id];
                if (worker) {
                    worker.send({ type: 'health-check', timestamp: Date.now() });
                }
            }
        }, healthCheckInterval);
    }

    async gracefulShutdown() {
        if (this.isShuttingDown) {
            console.log(chalk.yellow('[Master] Shutdown already in progress...'));
            return;
        }

        this.isShuttingDown = true;
        console.log(chalk.yellow('[Master] Starting graceful shutdown...'));

        const shutdownTimeout = 30000; // 30 seconds
        const workers = Object.values(cluster.workers);

        if (workers.length === 0) {
            console.log(chalk.green('[Master] No workers to shutdown, exiting...'));
            process.exit(0);
        }

        // Send shutdown signal to all workers
        workers.forEach(worker => {
            if (worker) {
                console.log(chalk.yellow(`[Master] Sending shutdown signal to worker ${worker.process.pid}`));
                worker.send({ type: 'shutdown' });
                worker.disconnect();
            }
        });

        // Wait for workers to exit gracefully
        const shutdownPromise = new Promise((resolve) => {
            const checkWorkers = () => {
                const activeWorkers = Object.keys(cluster.workers).length;
                if (activeWorkers === 0) {
                    resolve();
                } else {
                    setTimeout(checkWorkers, 1000);
                }
            };
            checkWorkers();
        });

        // Force shutdown after timeout
        const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => {
                console.log(chalk.red('[Master] Shutdown timeout reached, forcing worker termination...'));
                workers.forEach(worker => {
                    if (worker && !worker.isDead()) {
                        console.log(chalk.red(`[Master] Force killing worker ${worker.process.pid}`));
                        worker.kill('SIGKILL');
                    }
                });
                resolve();
            }, shutdownTimeout);
        });

        await Promise.race([shutdownPromise, timeoutPromise]);
        
        console.log(chalk.green('[Master] Graceful shutdown completed'));
        process.exit(0);
    }

    emergencyShutdown() {
        console.log(chalk.red('[Master] Emergency shutdown initiated...'));
        
        // Kill all workers immediately
        for (const id in cluster.workers) {
            const worker = cluster.workers[id];
            if (worker && !worker.isDead()) {
                console.log(chalk.red(`[Master] Emergency killing worker ${worker.process.pid}`));
                worker.kill('SIGKILL');
            }
        }
        
        process.exit(1);
    }

    getStatus() {
        return {
            masterPid: process.pid,
            environment: env,
            maxWorkers: this.maxWorkers,
            activeWorkers: Object.keys(cluster.workers).length,
            isShuttingDown: this.isShuttingDown,
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            workers: Object.values(cluster.workers).map(worker => ({
                id: worker.id,
                pid: worker.process.pid,
                state: worker.state,
                isDead: worker.isDead()
            }))
        };
    }
}

// Only run if this is the master process
if (cluster.isPrimary) {
    const master = new MasterProcess();
    
    // Expose status endpoint for monitoring
    process.on('message', (message) => {
        if (message.type === 'status') {
            process.send({
                type: 'status-response',
                data: master.getStatus()
            });
        }
    });
    
    console.log(chalk.green('[Master] Cluster master process started successfully'));
} else {
    // This should not happen as we're setting up the master
    console.error(chalk.red('[Master] This file should only be run as master process'));
    process.exit(1);
}

module.exports = MasterProcess;
