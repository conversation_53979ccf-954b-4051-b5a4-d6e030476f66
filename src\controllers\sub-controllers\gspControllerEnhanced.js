const axios = require('axios');
const { redisSet, redisGet } = require('../../utils/redisUtils');
const axiosUtils = require('../../utils/axiosUtils');
const chalk = require('chalk');
const taxUtils = require('../../utils/taxUtils');
const { v4: uuidv4 } = require('uuid');

require('dotenv').config();

const env = process.env.HOST_ENV || process.env.NODE_ENV || 'development';
const gspConfig = require('../../config/envConfig')[env];

// Enhanced configuration with validation
const validateGspConfig = () => {
    const requiredFields = ['gsp_client_id', 'gsp_client_secret', 'gsp_user_name', 'gsp_password', 'gsp_base_url'];
    const missingFields = requiredFields.filter(field => !gspConfig[field]);
    
    if (missingFields.length > 0) {
        throw new Error(`Missing GSP configuration: ${missingFields.join(', ')}`);
    }
};

// Validate configuration on module load
validateGspConfig();

let local_basedUrl = gspConfig.gsp_base_url;

// Only add /test suffix in development
if (env === 'development') {
    local_basedUrl = local_basedUrl + '/test';
}

// Constants for better maintainability
const GSP_CONSTANTS = {
    REDIS_KEYS: {
        ACCESS_TOKEN: 'GSP_ACCESS_TOKEN',
        RESPONSE: 'GSP_RESPONSE'
    },
    TIMEOUTS: {
        REQUEST: 30000, // 30 seconds
        TOKEN_REFRESH: 5000 // 5 seconds
    },
    RETRY: {
        MAX_ATTEMPTS: 3,
        DELAY: 1000 // 1 second
    }
};

/**
 * Enhanced GSP login with retry mechanism and better error handling
 */
const gspLogin = async (requestId = null) => {
    const logPrefix = `[GSP Login${requestId ? ` ${requestId}` : ''}]`;
    
    try {
        console.log(chalk.blue(`${logPrefix} Attempting GSP authentication...`));
        
        const response = await axiosUtils.globalSendPostRequest(
            {
                'gspappid': gspConfig.gsp_client_id,
                'gspappsecret': gspConfig.gsp_client_secret,
            },
            gspConfig.gsp_base_url,
            '/gsp/authenticate',
            null,
            GSP_CONSTANTS.TIMEOUTS.REQUEST
        );

        if (!response?.data) {
            throw new Error('Invalid response from GSP authentication service');
        }

        const { access_token, expires_in } = response.data;

        if (!access_token || !expires_in) {
            console.error(chalk.red(`${logPrefix} Missing access_token or expires_in in response`));
            throw new Error('Invalid authentication response: missing token or expiry');
        }

        // Store token in Redis with expiry (reduce by 5 minutes for safety)
        const safeExpiryTime = Math.max(expires_in - 300, 300); // At least 5 minutes
        
        await Promise.all([
            redisSet(GSP_CONSTANTS.REDIS_KEYS.RESPONSE, JSON.stringify(response.data), safeExpiryTime),
            redisSet(GSP_CONSTANTS.REDIS_KEYS.ACCESS_TOKEN, access_token, safeExpiryTime)
        ]);

        console.log(chalk.green(`${logPrefix} Authentication successful, token expires in ${safeExpiryTime}s`));

        return {
            success: true,
            message: 'GSP Login Successful',
            access_token,
            expires_in: safeExpiryTime
        };

    } catch (error) {
        console.error(chalk.red(`${logPrefix} Authentication failed:`), error.message);
        
        return {
            success: false,
            error: error.message || 'GSP authentication failed',
            details: error.response?.data || null
        };
    }
};

/**
 * Get valid access token with automatic refresh
 */
const getValidAccessToken = async (requestId = null) => {
    const logPrefix = `[GSP Token${requestId ? ` ${requestId}` : ''}]`;
    
    try {
        let accessToken = await redisGet(GSP_CONSTANTS.REDIS_KEYS.ACCESS_TOKEN);

        if (accessToken) {
            console.log(chalk.blue(`${logPrefix} Using cached access token`));
            return { success: true, access_token: accessToken };
        }

        console.warn(chalk.yellow(`${logPrefix} No cached token found, refreshing...`));
        
        const loginResponse = await gspLogin(requestId);
        
        if (!loginResponse.success) {
            return {
                success: false,
                error: 'Failed to obtain access token',
                details: loginResponse.error
            };
        }

        return {
            success: true,
            access_token: loginResponse.access_token
        };

    } catch (error) {
        console.error(chalk.red(`${logPrefix} Token retrieval failed:`), error);
        return {
            success: false,
            error: error.message || 'Token retrieval failed'
        };
    }
};

/**
 * Enhanced GSP request wrapper with retry logic
 */
const makeGspRequest = async (endpoint, data, headers = {}, requestId = null, retryCount = 0) => {
    const logPrefix = `[GSP Request${requestId ? ` ${requestId}` : ''}]`;
    
    try {
        console.log(chalk.blue(`${logPrefix} Making request to ${endpoint} (attempt ${retryCount + 1})`));
        
        const response = await axiosUtils.globalSendPostRequest(
            headers,
            local_basedUrl,
            endpoint,
            data,
            GSP_CONSTANTS.TIMEOUTS.REQUEST
        );

        if (!response?.data) {
            throw new Error('Invalid response from GSP service');
        }

        console.log(chalk.green(`${logPrefix} Request successful`));
        return { success: true, data: response.data };

    } catch (error) {
        console.error(chalk.red(`${logPrefix} Request failed (attempt ${retryCount + 1}):`), error.message);
        
        // Check if it's a token-related error and we haven't exceeded retry limit
        const isTokenError = error.response?.status === 401 || error.response?.status === 403;
        const canRetry = retryCount < GSP_CONSTANTS.RETRY.MAX_ATTEMPTS && isTokenError;
        
        if (canRetry) {
            console.warn(chalk.yellow(`${logPrefix} Token may be invalid, refreshing and retrying...`));
            
            // Clear cached token
            await redisSet(GSP_CONSTANTS.REDIS_KEYS.ACCESS_TOKEN, '', 1);
            
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, GSP_CONSTANTS.RETRY.DELAY * (retryCount + 1)));
            
            // Get new token
            const tokenResponse = await getValidAccessToken(requestId);
            if (tokenResponse.success) {
                // Update headers with new token
                const updatedHeaders = {
                    ...headers,
                    'Authorization': `Bearer ${tokenResponse.access_token}`
                };
                
                // Retry request
                return makeGspRequest(endpoint, data, updatedHeaders, requestId, retryCount + 1);
            }
        }
        
        return {
            success: false,
            error: error.message || 'GSP request failed',
            details: error.response?.data || null,
            statusCode: error.response?.status || 500
        };
    }
};

/**
 * Enhanced invoice creation with comprehensive validation and error handling
 */
const createGSPInvoice = async (data) => {
    const requestId = data.uid || uuidv4();
    const logPrefix = `[GSP Invoice ${requestId}]`;
    
    try {
        console.log(chalk.blue(`${logPrefix} Starting invoice creation process...`));
        
        // Validate required data
        if (!data.fromFirm || !data.toFirm) {
            return {
                success: false,
                error: 'Missing required firm details',
                details: 'Both fromFirm and toFirm are required'
            };
        }

        if (!Array.isArray(data.items) || data.items.length === 0) {
            return {
                success: false,
                error: 'Invalid or empty items list',
                details: 'At least one item is required for invoice creation'
            };
        }

        // Get valid access token
        const tokenResponse = await getValidAccessToken(requestId);
        if (!tokenResponse.success) {
            return {
                success: false,
                error: 'Authentication failed',
                details: tokenResponse.error
            };
        }

        const { fromCustomer, toCustomer } = { fromCustomer: data.fromFirm, toCustomer: data.toFirm };

        // Calculate tax values
        const invTaxVal = taxUtils.generateInvoiceValDtls({
            items: data.items,
            sellerGstin: fromCustomer.gstin,
            buyerGstin: toCustomer.gstin,
            otherCharges: data.otherCharges || 0,
            roundOff: data.roundOff || 0,
        });

        // Build GSP request data
        const gspRequestData = {
            Version: "1.1",
            TranDtls: {
                TaxSch: "GST",
                SupTyp: "B2B",
                RegRev: "N",
                EcmGstin: null,
                IgstOnIntra: data.igstOnIntra || "N",
            },
            DocDtls: {
                Typ: "INV",
                No: data.voucherNumber,
                Dt: data.voucherTime,
            },
            SellerDtls: {
                Gstin: fromCustomer.gstin,
                LglNm: fromCustomer.name,
                TrdNm: fromCustomer.name,
                Addr1: fromCustomer.address,
                Addr2: "",
                Loc: fromCustomer.state,
                Pin: fromCustomer.pincode,
                Stcd: fromCustomer.gstin.substring(0, 2),
                Em: fromCustomer.email || ""
            },
            BuyerDtls: {
                Gstin: toCustomer.gstin,
                LglNm: toCustomer.name,
                Pos: toCustomer.gstin.substring(0, 2),
                Addr1: toCustomer.address,
                Addr2: "",
                Loc: toCustomer.state,
                Pin: toCustomer.pincode,
                Stcd: toCustomer.gstin.substring(0, 2),
                Em: toCustomer.email || ""
            },
            DispDtls: {
                Nm: fromCustomer.name,
                Addr1: fromCustomer.address,
                Addr2: "...",
                Loc: fromCustomer.state,
                Pin: fromCustomer.pincode,
                Stcd: fromCustomer.gstin.substring(0, 2)
            },
            ShipDtls: {
                Gstin: toCustomer.gstin,
                LglNm: toCustomer.name,
                TrdNm: toCustomer.name,
                Addr1: toCustomer.address,
                Addr2: "...",
                Loc: toCustomer.state,
                Pin: toCustomer.pincode,
                Stcd: toCustomer.gstin.substring(0, 2),
            },
            ValDtls: {
                AssVal: invTaxVal.AssVal,
                CgstVal: invTaxVal.CgstVal,
                SgstVal: invTaxVal.SgstVal,
                IgstVal: invTaxVal.IgstVal,
                CesVal: invTaxVal.CesVal || 0,
                OthChrg: invTaxVal.OthChrg,
                Discount: data.discount || 0,
                TotInvVal: invTaxVal.TotInvVal,
                RndOffAmt: invTaxVal.RndOffAmt,
                TotInvValFc: invTaxVal.TotInvValFc || 0,
            },
            ExpDtls: {
                ShipBNo: null,
                ShipBDt: null,
                Port: null,
            },
            ItemList: []
        };

        // Process items
        let serialNumber = 1;
        for (const item of data.items) {
            if (!item.totalCost || item.gstRate === undefined) {
                return {
                    success: false,
                    error: 'Invalid item data',
                    details: `Item ${serialNumber}: totalCost and gstRate are required`
                };
            }

            const itemTaxVal = taxUtils.itemBasedGSTCalc({
                unitPrice: item.rate,
                quantity: item.quantity || 1,
                discount: item.discount || 0,
                gstRate: item.gstRate,
                sellerGstin: fromCustomer.gstin,
                buyerGstin: toCustomer.gstin,
                roundOff: 0,
                otherCharges: 0,
                cessRate: item.cessRate || 0,
                forceGstType: item.forceGstType || "IGST"
            });

            gspRequestData.ItemList.push({
                SlNo: serialNumber.toString(),
                PrdDesc: item.skuName || "",
                IsServc: item.isService === "Y" ? "Y" : "N",
                HsnCd: item.hsnCode || "",
                Qty: item.quantity || 1,
                Unit: item.unit || "PCS",
                UnitPrice: item.unitPrice || 0.00,
                TotAmt: item.totalCost,
                Discount: itemTaxVal.discount || 0.00,
                AssAmt: itemTaxVal.assVal || 0.00,
                GstRt: item.gstRate,
                IgstAmt: itemTaxVal.igst || 0.00,
                CgstAmt: itemTaxVal.cgst || 0.00,
                SgstAmt: itemTaxVal.sgst || 0.00,
                CesRt: item.cessRate || 0.00,
                CesAmt: itemTaxVal.cess || 0.00,
                TotItemVal: itemTaxVal.TotInvVal || 0.00,
            });
            serialNumber++;
        }

        // Prepare request headers
        const headers = {
            'Authorization': `Bearer ${tokenResponse.access_token}`,
            'Content-Type': 'application/json',
            'user_name': gspConfig.gsp_user_name,
            'password': gspConfig.gsp_password,
            'requestid': requestId,
            'gstin': fromCustomer.gstin,
        };

        // Make GSP request
        const gspResponse = await makeGspRequest('/enriched/ei/api/invoice', gspRequestData, headers, requestId);
        
        if (!gspResponse.success) {
            return {
                success: false,
                error: 'GSP service error',
                details: gspResponse.error,
                statusCode: gspResponse.statusCode
            };
        }

        if (!gspResponse.data?.result) {
            return {
                success: false,
                error: 'Invalid GSP response',
                details: 'Missing result data in GSP response'
            };
        }

        console.log(chalk.green(`${logPrefix} Invoice created successfully`));
        
        return {
            success: true,
            data: {
                controlCode: gspResponse.data.result.Irn,
                documentNumber: gspResponse.data.result.AckNo,
                documentDate: gspResponse.data.result.AckDt,
                qrCode: gspResponse.data.result.SignedQRCode,
                status: gspResponse.data.result.Status,
                EwbNo: gspResponse.data.result.EwbNo || "",
                EwbDt: gspResponse.data.result.EwbDt || "",
                EwbValidTill: gspResponse.data.result.EwbValidTill || "",
                SignedInvoice: gspResponse.data.result.SignedInvoice || "",
                requestId
            }
        };

    } catch (error) {
        console.error(chalk.red(`${logPrefix} Unexpected error:`), error);
        return {
            success: false,
            error: 'Unexpected error during invoice creation',
            details: error.message,
            requestId
        };
    }
};

module.exports = {
    gspLogin,
    getValidAccessToken,
    createGSPInvoice,
    makeGspRequest,
    GSP_CONSTANTS
};
