# 📋 GSP Invoice API Documentation

## 🚀 Overview

This document provides comprehensive documentation for all 4 GSP Invoice API endpoints. These APIs handle complete invoice lifecycle management including creation, cancellation, and retrieval operations.

## 🔗 Base URL
```
http://localhost:5000/api/invoice
```

## 🔐 Authentication

All endpoints require authentication via Bearer token in the header:
```
Authorization: Bearer your-access-token
```

## 📊 API Endpoints

### 1. 🆕 Create Invoice

**Endpoint:** `POST /api/invoice/create`

**Description:** Creates a new GSP invoice with complete validation and tax calculations.

**Request Body:**
```json
{
  "invoiceData": {
    "voucherNumber": "INV-001",
    "voucherTime": "2024-01-15",
    "fromFirm": {
      "gstin": "29ABCDE1234F1Z5",
      "name": "Seller Company Pvt Ltd",
      "address": "123 Business Street, Commercial Area",
      "state": "Karnataka",
      "pincode": "560001",
      "email": "<EMAIL>"
    },
    "toFirm": {
      "gstin": "27FGHIJ5678K2L9",
      "name": "Buyer Corporation Ltd",
      "address": "456 Purchase Avenue, Industrial Zone",
      "state": "Maharashtra",
      "pincode": "400001",
      "email": "<EMAIL>"
    },
    "items": [
      {
        "skuName": "Premium Widget",
        "hsnCode": "8471",
        "quantity": 10,
        "rate": 1000.00,
        "totalCost": 10000.00,
        "gstRate": 18,
        "unit": "PCS",
        "isService": "N",
        "cessRate": 0,
        "discount": 0
      }
    ],
    "igstOnIntra": "N",
    "discount": 0,
    "otherCharges": 0,
    "roundOff": 0
  }
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "GSP Invoice created successfully",
  "data": {
    "controlCode": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "documentNumber": "ACK123456789",
    "documentDate": "2024-01-15 10:30:00",
    "qrCode": "base64-encoded-qr-code-data",
    "status": "ACT",
    "EwbNo": "123456789012",
    "EwbDt": "2024-01-15 10:30:00",
    "EwbValidTill": "2024-01-16 23:59:59",
    "SignedInvoice": "digitally-signed-invoice-data",
    "requestId": "uuid-request-id"
  },
  "requestId": "uuid-request-id",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 2. ❌ Cancel Invoice

**Endpoint:** `POST /api/invoice/cancel`

**Description:** Cancels an existing GSP invoice using its IRN.

**Request Body:**
```json
{
  "irn": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
  "cancelReason": "1",
  "cancelRemark": "Duplicate invoice created by mistake",
  "gstin": "29ABCDE1234F1Z5"
}
```

**Cancel Reason Codes:**
- `"1"` - Duplicate
- `"2"` - Data Entry Error
- `"3"` - Order Cancelled
- `"4"` - Others

**Success Response (200):**
```json
{
  "success": true,
  "message": "Invoice cancelled successfully",
  "data": {
    "controlCode": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "cancelDate": "2024-01-15 15:45:00"
  },
  "requestId": "uuid-request-id",
  "timestamp": "2024-01-15T15:45:00.000Z"
}
```

### 3. 🔍 Get Invoice by IRN

**Endpoint:** `POST /api/invoice/get/:irn`

**Description:** Retrieves complete invoice details using the Invoice Reference Number (IRN).

**URL Parameters:**
- `irn` (string, required): 64-character Invoice Reference Number

**Request Body:**
```json
{
  "gstin": "29ABCDE1234F1Z5"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Invoice retrieved successfully",
  "data": {
    "controlCode": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "documentNumber": "ACK123456789",
    "documentDate": "2024-01-15 10:30:00",
    "qrCode": "base64-encoded-qr-code-data",
    "status": "ACT",
    "EwbNo": "123456789012",
    "EwbDt": "2024-01-15 10:30:00",
    "EwbValidTill": "2024-01-16 23:59:59"
  },
  "requestId": "uuid-request-id",
  "timestamp": "2024-01-15T16:00:00.000Z"
}
```

### 4. 📄 Get Invoice by Document Number

**Endpoint:** `POST /api/invoice/document/:documentNumber`

**Description:** Retrieves invoice details using the document/voucher number.

**URL Parameters:**
- `documentNumber` (string, required): Invoice document number

**Request Body:**
```json
{
  "gstin": "29ABCDE1234F1Z5"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Invoice retrieved successfully",
  "data": {
    "controlCode": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "documentNumber": "ACK123456789",
    "documentDate": "2024-01-15 10:30:00",
    "qrCode": "base64-encoded-qr-code-data",
    "status": "ACT",
    "EwbNo": "123456789012",
    "EwbDt": "2024-01-15 10:30:00",
    "EwbValidTill": "2024-01-16 23:59:59"
  },
  "requestId": "uuid-request-id",
  "timestamp": "2024-01-15T16:15:00.000Z"
}
```

## ❌ Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "field": "invoiceData.fromFirm.gstin",
      "message": "GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)"
    }
  ],
  "requestId": "uuid-request-id",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Authentication Error (401)
```json
{
  "success": false,
  "error": "Unauthorized: Bearer token required",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### GSP Service Error (400)
```json
{
  "success": false,
  "error": "GSP service error",
  "details": "Invalid GSTIN provided",
  "requestId": "uuid-request-id",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Internal Server Error (500)
```json
{
  "success": false,
  "error": "Internal server error",
  "message": "An unexpected error occurred",
  "requestId": "uuid-request-id",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🔧 Validation Rules

### GSTIN Format
- Pattern: `^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$`
- Example: `29ABCDE1234F1Z5`

### HSN Code Format
- Pattern: `^[0-9]{4,8}$`
- Length: 4-8 digits

### Pincode Format
- Pattern: `^[0-9]{6}$`
- Length: Exactly 6 digits

### IRN Format
- Length: Exactly 64 characters
- Contains: Alphanumeric characters

## 📊 Rate Limiting

- **Authentication endpoints**: 5 requests per 15 minutes
- **API endpoints**: 30 requests per minute
- **General**: 100 requests per 15 minutes

## 🔍 Health Check

**Endpoint:** `GET /api/invoice/health`

**Response:**
```json
{
  "status": "healthy",
  "service": "gsp-invoice-service",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "version": "2.0.0",
  "endpoints": {
    "create": "POST /api/invoice/create",
    "cancel": "POST /api/invoice/cancel",
    "getByIrn": "POST /api/invoice/get/:irn",
    "getByDocument": "POST /api/invoice/document/:documentNumber"
  }
}
```

## 🧪 Testing Examples

### Using cURL

#### Create Invoice
```bash
curl -X POST http://localhost:5000/api/invoice/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "invoiceData": {
      "voucherNumber": "INV-001",
      "voucherTime": "2024-01-15",
      "fromFirm": {
        "gstin": "29ABCDE1234F1Z5",
        "name": "Test Company",
        "address": "Test Address",
        "state": "Karnataka",
        "pincode": "560001"
      },
      "toFirm": {
        "gstin": "27FGHIJ5678K2L9",
        "name": "Buyer Company",
        "address": "Buyer Address",
        "state": "Maharashtra",
        "pincode": "400001"
      },
      "items": [{
        "skuName": "Test Product",
        "hsnCode": "1234",
        "quantity": 1,
        "rate": 1000,
        "totalCost": 1000,
        "gstRate": 18
      }]
    }
  }'
```

#### Cancel Invoice
```bash
curl -X POST http://localhost:5000/api/invoice/cancel \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "irn": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "cancelReason": "1",
    "cancelRemark": "Test cancellation",
    "gstin": "29ABCDE1234F1Z5"
  }'
```

#### Get Invoice by IRN
```bash
curl -X POST http://localhost:5000/api/invoice/get/1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "gstin": "29ABCDE1234F1Z5"
  }'
```

#### Get Invoice by Document Number
```bash
curl -X POST http://localhost:5000/api/invoice/document/INV-001 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "gstin": "29ABCDE1234F1Z5"
  }'
```

## 🎯 Best Practices

1. **Always include request tracking**: Use unique UIDs for better debugging
2. **Validate data client-side**: Reduce server load with pre-validation
3. **Handle rate limits**: Implement exponential backoff for retries
4. **Store IRN safely**: Keep Invoice Reference Numbers for future operations
5. **Monitor responses**: Log all API interactions for audit trails
6. **Use HTTPS**: Always use secure connections in production
7. **Implement timeouts**: Set appropriate request timeouts (30 seconds recommended)

## 🔧 Integration Notes

- All endpoints return consistent response formats
- Request IDs are included for tracking and debugging
- Comprehensive validation prevents invalid data submission
- Rate limiting protects against abuse
- Detailed error messages help with troubleshooting
- Health check endpoint for monitoring service status
