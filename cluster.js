#!/usr/bin/env node

/**
 * Enhanced Cluster Master Process for GSP Invoice Service
 * 
 * This script provides:
 * - Master process management with worker clustering
 * - Graceful shutdown and restart capabilities
 * - Health monitoring and alerting
 * - Process metrics and logging
 * - Zero-downtime deployments
 * 
 * Usage:
 *   node cluster.js                    # Start with default settings
 *   WORKERS=4 node cluster.js          # Start with 4 workers
 *   NODE_ENV=production node cluster.js # Start in production mode
 */

require('dotenv').config();

const cluster = require('cluster');
const os = require('os');
const chalk = require('chalk');
const path = require('path');

// Configuration
const env = process.env.NODE_ENV || process.env.HOST_ENV || 'development';
const config = require('./src/config/envConfig')[env];

class ClusterManager {
    constructor() {
        this.workers = new Map();
        this.isShuttingDown = false;
        this.maxWorkers = parseInt(process.env.WORKERS) || config.max_workers || os.cpus().length;
        this.workerRestartDelay = parseInt(process.env.RESTART_DELAY) || 5000;
        this.maxRestarts = parseInt(process.env.MAX_RESTARTS) || 5;
        this.restartCounts = new Map();
        this.shutdownTimeout = parseInt(process.env.SHUTDOWN_TIMEOUT) || 30000;
        
        // Metrics
        this.metrics = {
            startTime: Date.now(),
            totalRestarts: 0,
            totalCrashes: 0,
            alerts: []
        };
        
        console.log(chalk.blue('='.repeat(60)));
        console.log(chalk.blue('🚀 GSP Invoice Service Cluster Manager'));
        console.log(chalk.blue('='.repeat(60)));
        console.log(chalk.green(`Environment: ${env}`));
        console.log(chalk.green(`Master PID: ${process.pid}`));
        console.log(chalk.green(`Max Workers: ${this.maxWorkers}`));
        console.log(chalk.green(`CPU Cores: ${os.cpus().length}`));
        console.log(chalk.blue('='.repeat(60)));
        
        this.init();
    }

    init() {
        this.setupCluster();
        this.setupSignalHandlers();
        this.startHealthMonitoring();
        this.startMetricsCollection();
        this.forkWorkers();
    }

    setupCluster() {
        cluster.setupPrimary({
            exec: path.join(__dirname, 'server.js'),
            silent: false,
            env: {
                ...process.env,
                CLUSTER_MODE: 'true'
            }
        });

        // Worker event handlers
        cluster.on('fork', (worker) => {
            console.log(chalk.green(`[Master] 🔄 Worker ${worker.process.pid} forked`));
            this.workers.set(worker.id, {
                worker,
                startTime: Date.now(),
                restarts: this.restartCounts.get(worker.id) || 0,
                lastHealthCheck: Date.now()
            });
        });

        cluster.on('online', (worker) => {
            console.log(chalk.green(`[Master] ✅ Worker ${worker.process.pid} is online`));
        });

        cluster.on('listening', (worker, address) => {
            console.log(chalk.green(`[Master] 🎧 Worker ${worker.process.pid} listening on ${address.address}:${address.port}`));
        });

        cluster.on('disconnect', (worker) => {
            console.log(chalk.yellow(`[Master] 🔌 Worker ${worker.process.pid} disconnected`));
        });

        cluster.on('exit', (worker, code, signal) => {
            console.log(chalk.red(`[Master] 💀 Worker ${worker.process.pid} died (${signal || code})`));
            this.handleWorkerExit(worker, code, signal);
        });

        // Handle worker messages
        cluster.on('message', (worker, message) => {
            this.handleWorkerMessage(worker, message);
        });
    }

    forkWorkers() {
        for (let i = 0; i < this.maxWorkers; i++) {
            this.forkWorker();
        }
    }

    forkWorker() {
        if (this.isShuttingDown) {
            return null;
        }

        const worker = cluster.fork();
        
        // Set up worker-specific message handling
        worker.on('message', (message) => {
            this.handleWorkerMessage(worker, message);
        });

        return worker;
    }

    handleWorkerExit(worker, code, signal) {
        this.metrics.totalCrashes++;
        this.workers.delete(worker.id);

        if (this.isShuttingDown) {
            console.log(chalk.blue(`[Master] Worker ${worker.process.pid} exited during shutdown`));
            this.checkShutdownComplete();
            return;
        }

        const restartCount = this.restartCounts.get(worker.id) || 0;
        
        if (restartCount >= this.maxRestarts) {
            console.error(chalk.red(`[Master] ❌ Worker ${worker.id} exceeded max restarts (${this.maxRestarts}). Not restarting.`));
            this.recordAlert('worker_max_restarts', `Worker ${worker.id} exceeded maximum restart attempts`);
            return;
        }

        // Increment restart count
        this.restartCounts.set(worker.id, restartCount + 1);
        this.metrics.totalRestarts++;

        console.log(chalk.yellow(`[Master] 🔄 Restarting worker ${worker.id} in ${this.workerRestartDelay}ms (attempt ${restartCount + 1}/${this.maxRestarts})`));
        
        setTimeout(() => {
            if (!this.isShuttingDown) {
                this.forkWorker();
            }
        }, this.workerRestartDelay);
    }

    handleWorkerMessage(worker, message) {
        switch (message.type) {
            case 'health-check':
                worker.send({ 
                    type: 'health-response', 
                    timestamp: Date.now() 
                });
                
                // Update last health check time
                const workerInfo = this.workers.get(worker.id);
                if (workerInfo) {
                    workerInfo.lastHealthCheck = Date.now();
                }
                break;
            
            case 'worker-ready':
                console.log(chalk.green(`[Master] ✅ Worker ${worker.process.pid} is ready`));
                break;
            
            case 'worker-error':
                console.error(chalk.red(`[Master] ❌ Worker ${worker.process.pid} reported error:`), message.error);
                this.recordAlert('worker_error', `Worker ${worker.process.pid}: ${message.error}`);
                break;
            
            case 'alert':
                this.recordAlert(message.data.type, message.data);
                break;
            
            case 'graceful-shutdown':
                console.log(chalk.yellow(`[Master] 🛑 Worker ${worker.process.pid} requesting graceful shutdown`));
                this.gracefulShutdown();
                break;
            
            default:
                console.log(chalk.blue(`[Master] 📨 Message from worker ${worker.process.pid}:`), message);
        }
    }

    setupSignalHandlers() {
        // Graceful shutdown signals
        process.on('SIGTERM', () => {
            console.log(chalk.yellow('[Master] 🛑 Received SIGTERM, initiating graceful shutdown...'));
            this.gracefulShutdown();
        });

        process.on('SIGINT', () => {
            console.log(chalk.yellow('[Master] 🛑 Received SIGINT, initiating graceful shutdown...'));
            this.gracefulShutdown();
        });

        // Reload workers on SIGUSR2
        process.on('SIGUSR2', () => {
            console.log(chalk.blue('[Master] 🔄 Received SIGUSR2, reloading workers...'));
            this.reloadWorkers();
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error(chalk.red('[Master] 💥 Uncaught Exception:'), error);
            this.recordAlert('uncaught_exception', error.message);
            this.emergencyShutdown();
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            console.error(chalk.red('[Master] 💥 Unhandled Rejection:'), reason);
            this.recordAlert('unhandled_rejection', reason);
            this.emergencyShutdown();
        });
    }

    startHealthMonitoring() {
        const healthCheckInterval = 30000; // 30 seconds
        
        setInterval(() => {
            if (this.isShuttingDown) return;
            
            const activeWorkers = Object.keys(cluster.workers).length;
            console.log(chalk.blue(`[Master] 💓 Health check - Active workers: ${activeWorkers}/${this.maxWorkers}`));
            
            // Check if we need to restart any workers
            if (activeWorkers < this.maxWorkers && !this.isShuttingDown) {
                console.log(chalk.yellow(`[Master] ⚠️  Worker count below target, forking new worker`));
                this.forkWorker();
            }
            
            // Check worker health
            this.checkWorkerHealth();
            
        }, healthCheckInterval);
    }

    checkWorkerHealth() {
        const now = Date.now();
        const healthTimeout = 60000; // 1 minute
        
        for (const [workerId, workerInfo] of this.workers) {
            const timeSinceLastCheck = now - workerInfo.lastHealthCheck;
            
            if (timeSinceLastCheck > healthTimeout) {
                console.warn(chalk.yellow(`[Master] ⚠️  Worker ${workerId} hasn't responded to health check for ${timeSinceLastCheck}ms`));
                
                // Kill unresponsive worker
                if (timeSinceLastCheck > healthTimeout * 2) {
                    console.error(chalk.red(`[Master] 💀 Killing unresponsive worker ${workerId}`));
                    workerInfo.worker.kill('SIGKILL');
                }
            }
        }
    }

    startMetricsCollection() {
        setInterval(() => {
            if (this.isShuttingDown) return;
            
            const uptime = process.uptime();
            const memUsage = process.memoryUsage();
            
            console.log(chalk.blue(`[Master] 📊 Uptime: ${Math.round(uptime / 60)}m, Memory: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB, Restarts: ${this.metrics.totalRestarts}`));
            
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    recordAlert(type, data) {
        const alert = {
            timestamp: new Date().toISOString(),
            type,
            data
        };
        
        this.metrics.alerts.push(alert);
        
        // Keep only last 100 alerts
        if (this.metrics.alerts.length > 100) {
            this.metrics.alerts = this.metrics.alerts.slice(-100);
        }
        
        console.error(chalk.red(`[Master] 🚨 ALERT [${type}]:`, data));
    }

    async reloadWorkers() {
        console.log(chalk.blue('[Master] 🔄 Starting zero-downtime worker reload...'));
        
        const workers = Object.values(cluster.workers);
        
        for (const worker of workers) {
            console.log(chalk.blue(`[Master] 🔄 Reloading worker ${worker.process.pid}...`));
            
            // Fork new worker first
            const newWorker = this.forkWorker();
            
            // Wait for new worker to be ready
            await new Promise((resolve) => {
                const checkReady = () => {
                    if (newWorker.state === 'listening') {
                        resolve();
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                checkReady();
            });
            
            // Gracefully shutdown old worker
            worker.disconnect();
            
            setTimeout(() => {
                if (!worker.isDead()) {
                    worker.kill('SIGTERM');
                }
            }, 5000);
            
            // Wait a bit before reloading next worker
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log(chalk.green('[Master] ✅ Worker reload completed'));
    }

    async gracefulShutdown() {
        if (this.isShuttingDown) {
            console.log(chalk.yellow('[Master] 🛑 Shutdown already in progress...'));
            return;
        }

        this.isShuttingDown = true;
        console.log(chalk.yellow('[Master] 🛑 Starting graceful shutdown...'));

        const workers = Object.values(cluster.workers);

        if (workers.length === 0) {
            console.log(chalk.green('[Master] ✅ No workers to shutdown, exiting...'));
            process.exit(0);
        }

        // Send shutdown signal to all workers
        workers.forEach(worker => {
            if (worker) {
                console.log(chalk.yellow(`[Master] 🛑 Sending shutdown signal to worker ${worker.process.pid}`));
                worker.send({ type: 'shutdown' });
                worker.disconnect();
            }
        });

        // Set timeout for forced shutdown
        const forceTimeout = setTimeout(() => {
            console.log(chalk.red('[Master] ⏰ Shutdown timeout reached, forcing worker termination...'));
            workers.forEach(worker => {
                if (worker && !worker.isDead()) {
                    console.log(chalk.red(`[Master] 💀 Force killing worker ${worker.process.pid}`));
                    worker.kill('SIGKILL');
                }
            });
            process.exit(1);
        }, this.shutdownTimeout);

        // Wait for all workers to exit
        this.shutdownTimeout = forceTimeout;
    }

    checkShutdownComplete() {
        const activeWorkers = Object.keys(cluster.workers).length;
        
        if (activeWorkers === 0) {
            console.log(chalk.green('[Master] ✅ All workers have exited, shutting down master...'));
            clearTimeout(this.shutdownTimeout);
            process.exit(0);
        }
    }

    emergencyShutdown() {
        console.log(chalk.red('[Master] 🚨 Emergency shutdown initiated...'));
        
        // Kill all workers immediately
        for (const id in cluster.workers) {
            const worker = cluster.workers[id];
            if (worker && !worker.isDead()) {
                console.log(chalk.red(`[Master] 💀 Emergency killing worker ${worker.process.pid}`));
                worker.kill('SIGKILL');
            }
        }
        
        process.exit(1);
    }

    getStatus() {
        return {
            masterPid: process.pid,
            environment: env,
            maxWorkers: this.maxWorkers,
            activeWorkers: Object.keys(cluster.workers).length,
            isShuttingDown: this.isShuttingDown,
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            metrics: this.metrics,
            workers: Object.values(cluster.workers).map(worker => ({
                id: worker.id,
                pid: worker.process.pid,
                state: worker.state,
                isDead: worker.isDead()
            }))
        };
    }
}

// Only run if this is the master process
if (cluster.isPrimary) {
    new ClusterManager();
} else {
    console.error(chalk.red('[Cluster] This file should only be run as master process'));
    process.exit(1);
}
