{"name": "footprints-e-invoice-server", "version": "1.0.0", "description": "A RESTful API middleware server that bridges your core application (e.g., Recibo Server) with third-party e-invoicing service providers such as the Adaequare GSP API.", "private": true, "scripts": {"start": "node ./server.js", "start:cluster": "node ./cluster.js", "start:production": "NODE_ENV=production node ./cluster.js", "dev": "nodemon ./server.js", "dev:cluster": "FORCE_CLUSTER=true nodemon ./cluster.js", "processor": "node ./src/model/DBClient.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"Lin<PERSON> not configured\" && exit 0"}, "keywords": ["E-Invoice", "GSP", "Adaequare", "API", "Middleware"], "author": "Recibo Technology", "license": "ISC", "dependencies": {"axios": "^1.6.7", "body-parser": "^1.20.2", "chalk": "^4.1.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "helmet": "^7.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mysql2": "^3.6.0", "path": "^0.12.7", "process": "^0.11.10", "redis": "^4.6.7", "sequelize": "^6.37.0", "uuid": "^9.0.1", "validator": "^13.15.15", "winston": "^3.10.0"}, "devDependencies": {"nodemon": "^3.0.3"}}