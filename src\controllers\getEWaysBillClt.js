// ============================================================================
// Get E-Way Bill Controller with Country-wise Processing
// ============================================================================
// This controller handles E-Way Bill retrieval with:
// - Country-specific processing logic (similar to createInvoiceClt)
// - Company validation and lookup
// - Comprehensive input validation
// - Enhanced error handling and logging
// - Request tracking and monitoring
// - Support for E-Way Bill retrieval by IRN
// ============================================================================

const gspController = require('./sub-controllers/gspController');
const database = require('../model/DBClient');
const logs = require('../utils/utils');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const { ProcessMonitor } = require('../utils/processMonitor');

// Get process monitor instance
const processMonitor = ProcessMonitor ? new ProcessMonitor() : null;

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

// Get E-Way Bill by IRN validation schema
const getEWaysBillValidationSchema = Joi.object({
    ewaysBillData: Joi.object({
        // Invoice Reference Number (IRN) - required for E-Way Bill retrieval
        irn: Joi.string().required().length(64).messages({
            'string.length': 'IRN must be exactly 64 characters',
            'string.empty': 'IRN is required for E-Way Bill retrieval'
        }),

        // GSTIN of the company requesting the E-Way Bill
        gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)',
            'string.empty': 'GSTIN is required for E-Way Bill retrieval'
        }),

        // Optional tracking ID
        uid: Joi.string().uuid().optional()
    }).required()
});

// ============================================================================
// MAIN CONTROLLER FUNCTION
// ============================================================================

const getEWaysBillClt = async (req, res) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || uuidv4();

    try {
        // ========================================================================
        // STEP 1: REQUEST LOGGING AND VALIDATION
        // ========================================================================

        console.log(chalk.blue(`[Get E-Way Bill] Request ${requestId} started - IP: ${req.ip}`));

        // Validate input data
        const { error, value } = getEWaysBillValidationSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            console.warn(chalk.yellow(`[Get E-Way Bill] Validation failed for request ${requestId}:`, validationErrors));

            // Log validation failure
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'get_eway_bill',
                JSON.stringify(req.body),
                JSON.stringify({ error: 'Validation failed', details: validationErrors }),
                'VALIDATION_ERROR',
                0
            );

            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: validationErrors,
                requestId
            });
        }

        const { ewaysBillData } = value;

        // Add request ID to E-Way Bill data for tracking
        ewaysBillData.uid = ewaysBillData.uid || requestId;

        console.log(chalk.blue(`[Get E-Way Bill] Retrieving E-Way Bill with IRN: ${ewaysBillData.irn.substring(0, 20)}...`));

        // ========================================================================
        // STEP 2: COMPANY VALIDATION AND LOOKUP
        // ========================================================================

        // Get company from authenticated request (should be set by auth middleware)
        let company = req.company;

        // Fallback to header-based lookup (for backward compatibility)
        if (!company) {
            const companyUid = req.headers['company-uid'];

            if (!companyUid) {
                console.warn(chalk.yellow(`[Get E-Way Bill] No company UID provided for request ${requestId}`));
                return res.status(400).json({
                    success: false,
                    error: 'Company UID is required',
                    requestId
                });
            }

            company = await database.CompanyInfo.findOne({
                attributes: ['companyUid', 'country_name', 'country_code'],
                raw: true,
                where: { companyUid: companyUid }
            });
        }

        if (!company) {
            console.warn(chalk.yellow(`[Get E-Way Bill] Company not found for request ${requestId}`));

            // Log company not found
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'get_eway_bill',
                JSON.stringify({ companyUid: req.headers['company-uid'] }),
                JSON.stringify({ error: 'Company not found' }),
                'COMPANY_NOT_FOUND',
                0
            );

            return res.status(404).json({
                success: false,
                error: 'Company not found',
                requestId
            });
        }

        console.log(chalk.green(`[Get E-Way Bill] Processing retrieval for company ${company.companyUid} (${company.country_code}) - Request ${requestId}`));

        // ========================================================================
        // STEP 3: COUNTRY-WISE PROCESSING
        // ========================================================================

        let response;
        let apiStatus = 1; // Success by default

        switch (company.country_code) {
            case 'IND':
                try {
                    console.log(chalk.blue(`[Get E-Way Bill] Processing Indian E-Way Bill retrieval for request ${requestId}`));

                    // Map the data to match GSP controller expectations
                    const gspData = {
                        ...ewaysBillData,
                        bcontrolCode: ewaysBillData.irn  // GSP controller expects bcontrolCode
                    };

                    const gspResponse = await gspController.getGSPWayBill(gspData);

                    if (gspResponse.success === false || gspResponse.error) {
                        console.error(chalk.red(`[Get E-Way Bill] GSP retrieval failed for request ${requestId}:`, gspResponse.error));
                        apiStatus = 0;

                        // Log GSP error
                        await logs.createApiLogs(
                            database,
                            req.user?.uid || 'anonymous',
                            'get_gsp_eway_bill',
                            JSON.stringify(ewaysBillData),
                            JSON.stringify(gspResponse),
                            'GSP_ERROR',
                            0
                        );

                        return res.status(500).json({
                            success: false,
                            error: 'Failed to retrieve GSP E-Way Bill',
                            details: process.env.NODE_ENV === 'development' ? gspResponse.error : 'Internal processing error',
                            requestId
                        });
                    }

                    response = {
                        success: true,
                        message: 'GSP E-Way Bill retrieved successfully',
                        data: gspResponse.data || gspResponse,
                        requestId,
                        timestamp: new Date().toISOString()
                    };

                    console.log(chalk.green(`[Get E-Way Bill] GSP E-Way Bill retrieved successfully for request ${requestId}`));

                } catch (gspError) {
                    console.error(chalk.red(`[Get E-Way Bill] GSP processing error for request ${requestId}:`, gspError));
                    apiStatus = 0;

                    // Log GSP processing error
                    await logs.createApiLogs(
                        database,
                        req.user?.uid || 'anonymous',
                        'get_gsp_eway_bill',
                        JSON.stringify(ewaysBillData),
                        JSON.stringify({ error: gspError.message }),
                        'GSP_PROCESSING_ERROR',
                        0
                    );

                    return res.status(500).json({
                        success: false,
                        error: 'GSP processing failed',
                        details: process.env.NODE_ENV === 'development' ? gspError.message : 'Internal processing error',
                        requestId
                    });
                }
                break;

            case 'USA':
                console.warn(chalk.yellow(`[Get E-Way Bill] USA E-Way Bill retrieval not applicable for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    'get_eway_bill',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'E-Way Bill retrieval not applicable for USA' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'E-Way Bill retrieval is not applicable for USA',
                    details: 'E-Way Bill is specific to Indian GST system',
                    supportedCountries: ['IND'],
                    requestId
                });

            case 'CAN':
                console.warn(chalk.yellow(`[Get E-Way Bill] Canada E-Way Bill retrieval not applicable for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    'get_eway_bill',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'E-Way Bill retrieval not applicable for Canada' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'E-Way Bill retrieval is not applicable for Canada',
                    details: 'E-Way Bill is specific to Indian GST system',
                    supportedCountries: ['IND'],
                    requestId
                });

            default:
                console.warn(chalk.yellow(`[Get E-Way Bill] Unsupported country code ${company.country_code} for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    'get_eway_bill',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'Unsupported country code' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'Unsupported country code for E-Way Bill retrieval',
                    details: 'E-Way Bill retrieval is currently only supported for Indian companies',
                    supportedCountries: ['IND'],
                    currentCountry: company.country_code,
                    requestId
                });
        }

        // ========================================================================
        // STEP 4: SUCCESS LOGGING AND RESPONSE
        // ========================================================================

        // Log successful operation
        await logs.createApiLogs(
            database,
            req.user?.uid || 'anonymous',
            'get_eway_bill',
            JSON.stringify(ewaysBillData),
            JSON.stringify(response),
            'SUCCESS',
            apiStatus
        );

        // Record metrics
        if (processMonitor) {
            const responseTime = Date.now() - startTime;
            processMonitor.recordRequest(responseTime);
        }

        console.log(chalk.green(`[Get E-Way Bill] Request ${requestId} completed successfully in ${Date.now() - startTime}ms`));

        return res.status(200).json(response);

    } catch (error) {
        console.error(chalk.red(`[Get E-Way Bill] Unexpected error for request ${requestId}:`), error);

        // Record error metrics
        if (processMonitor) {
            processMonitor.recordError(error);
        }

        // Log unexpected error
        try {
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'get_eway_bill',
                JSON.stringify(req.body),
                JSON.stringify({ error: error.message, stack: error.stack }),
                'UNEXPECTED_ERROR',
                0
            );
        } catch (logError) {
            console.error(chalk.red(`[Get E-Way Bill] Failed to log error for request ${requestId}:`), logError);
        }

        return res.status(500).json({
            success: false,
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
            requestId,
            timestamp: new Date().toISOString()
        });
    }
};

// ============================================================================
// MODULE EXPORTS
// ============================================================================

module.exports = {
    getEWaysBillClt,
    getEWaysBillValidationSchema
};