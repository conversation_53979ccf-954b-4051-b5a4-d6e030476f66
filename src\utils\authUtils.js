require('dotenv').config();

const chalk = require('chalk');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const database = require('../model/DBClient');
const env = process.env.HOST_ENV || process.env.NODE_ENV || 'development';
const config = require('../config/envConfig')[env];

if (!config) {
    console.error(chalk.red(`[Fatal] Invalid or missing config for environment: ${env}`));
    process.exit(1);
}

/**
 * Express middleware to validate GSP request headers.
 * Checks for required headers: email, company-uid, and access-token.
 * Validates the access token against a predefined value in the configuration.
 * Ensures the company exists in the database.
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Object} - JSON response with error message or proceeds to next middleware
 */

const validateToken = async (req, res, next) => {
    try {
        const headers = req.headers;

        const userUid = headers['user-uid'];
        const companyUid = headers['company-uid'];
        const accessTokenHeader = headers['access-token'];

console.log(chalk.blue(`[validateToken] Headers: ${JSON.stringify(headers)}`));

        if (!companyUid) {
            return res.status(401).json({ message: 'Unauthorized: Company UID is missing' });
        }

        if (!userUid) {
            return res.status(401).json({ message: 'Unauthorized: User UID is missing' });
        }

        if (!accessTokenHeader) {
            return res.status(401).json({ message: 'Unauthorized: Access Token is missing' });
        }

        if (accessTokenHeader !== config.based_token) {
            return res.status(401).json({ message: 'Unauthorized: Invalid Access Token' });
        }

        const company = await database.CompanyInfo.findOne({
            where: { companyUid: companyUid }
        });

        if (!company) {
            return res.status(401).json({ message: 'Unauthorized: Company not registered' });
        }

        req.company = company;

        next();

    } catch (error) {
        console.error(chalk.red('Error during token validation:'), error);
        return res.status(500).json({ message: 'Internal Server Error during token validation' });
    }
};



/**
 * Generate JWT token for authenticated users
 * @param {Object} payload - User data to include in token
 * @param {string} expiresIn - Token expiration time
 * @returns {string} - JWT token
 */
const generateJWT = (payload, expiresIn = '1h') => {
    try {
        return jwt.sign(payload, config.jwt_secret, {
            expiresIn,
            issuer: 'footprints-e-invoice',
            audience: 'gsp-api'
        });
    } catch (error) {
        console.error(chalk.red('Error generating JWT:'), error);
        throw new Error('Token generation failed');
    }
};

/**
 * Verify JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} - Decoded token payload
 */
const verifyJWT = (token) => {
    try {
        return jwt.verify(token, config.jwt_secret);
    } catch (error) {
        console.error(chalk.red('Error verifying JWT:'), error);
        throw new Error('Token verification failed');
    }
};

/**
 * Enhanced JWT authentication middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const authenticateJWT = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: 'Unauthorized: Bearer token required'
            });
        }

        const token = authHeader.substring(7);

        try {
            const decoded = verifyJWT(token);

            // Verify company still exists and is active
            if (decoded.companyUid) {
                const company = await database.CompanyInfo.findOne({
                    where: { companyUid: decoded.companyUid }
                });

                if (!company) {
                    return res.status(401).json({
                        error: 'Unauthorized: Company not found'
                    });
                }

                req.company = company;
            }

            req.user = decoded;
            console.log(chalk.green(`JWT Authentication successful for: ${decoded.email || 'unknown'}`));
            next();

        } catch (jwtError) {
            console.warn(chalk.yellow(`Invalid JWT token from IP: ${req.ip}`));
            return res.status(401).json({
                error: 'Unauthorized: Invalid or expired token'
            });
        }

    } catch (error) {
        console.error(chalk.red('JWT Authentication error:'), error);
        res.status(500).json({ error: 'Internal authentication error' });
    }
};

/**
 * Hash password using crypto
 * @param {string} password - Plain text password
 * @param {string} salt - Salt for hashing
 * @returns {string} - Hashed password
 */
const hashPassword = (password, salt = null) => {
    try {
        const actualSalt = salt || crypto.randomBytes(16).toString('hex');
        const hash = crypto.pbkdf2Sync(password, actualSalt, 10000, 64, 'sha512').toString('hex');
        return { hash, salt: actualSalt };
    } catch (error) {
        console.error(chalk.red('Error hashing password:'), error);
        throw new Error('Password hashing failed');
    }
};

/**
 * Verify password against hash
 * @param {string} password - Plain text password
 * @param {string} hash - Stored hash
 * @param {string} salt - Stored salt
 * @returns {boolean} - Whether password matches
 */
const verifyPassword = (password, hash, salt) => {
    try {
        const hashToVerify = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
        return hash === hashToVerify;
    } catch (error) {
        console.error(chalk.red('Error verifying password:'), error);
        return false;
    }
};

module.exports = {
    validateToken,
    generateJWT,
    verifyJWT,
    authenticateJWT,
    hashPassword,
    verifyPassword
};

