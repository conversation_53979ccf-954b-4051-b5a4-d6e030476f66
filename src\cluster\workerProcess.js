require('dotenv').config();

const chalk = require('chalk');
const cluster = require('cluster');

class WorkerProcess {
    constructor() {
        this.isShuttingDown = false;
        this.connections = new Set();
        this.healthCheckInterval = null;
        this.lastHealthCheck = Date.now();
        this.gracefulShutdownTimeout = 25000; // 25 seconds (less than master's 30s timeout)
        
        this.setupWorker();
        this.setupSignalHandlers();
        this.startHealthMonitoring();
    }

    setupWorker() {
        if (!cluster.isWorker) {
            console.error(chalk.red('[Worker] This module should only be used in worker processes'));
            return;
        }

        console.log(chalk.green(`[Worker ${process.pid}] Worker process started`));
        
        // Handle messages from master
        process.on('message', (message) => {
            this.handleMasterMessage(message);
        });

        // Track connections for graceful shutdown
        this.trackConnections();
        
        // Notify master that worker is ready
        this.sendToMaster({ type: 'worker-ready' });
    }

    handleMasterMessage(message) {
        switch (message.type) {
            case 'health-check':
                this.lastHealthCheck = Date.now();
                this.sendToMaster({ 
                    type: 'health-response', 
                    timestamp: Date.now(),
                    memoryUsage: process.memoryUsage(),
                    uptime: process.uptime(),
                    connections: this.connections.size
                });
                break;
            
            case 'shutdown':
                console.log(chalk.yellow(`[Worker ${process.pid}] Received shutdown signal from master`));
                this.gracefulShutdown();
                break;
            
            default:
                console.log(chalk.blue(`[Worker ${process.pid}] Received message from master:`, message));
        }
    }

    trackConnections() {
        // This will be called when the Express server is created
        this.trackServerConnections = (server) => {
            server.on('connection', (socket) => {
                this.connections.add(socket);
                
                socket.on('close', () => {
                    this.connections.delete(socket);
                });
                
                socket.on('error', (error) => {
                    console.error(chalk.red(`[Worker ${process.pid}] Socket error:`, error));
                    this.connections.delete(socket);
                });
            });
        };
    }

    setupSignalHandlers() {
        // Handle SIGTERM
        process.on('SIGTERM', () => {
            console.log(chalk.yellow(`[Worker ${process.pid}] Received SIGTERM`));
            this.gracefulShutdown();
        });

        // Handle SIGINT
        process.on('SIGINT', () => {
            console.log(chalk.yellow(`[Worker ${process.pid}] Received SIGINT`));
            this.gracefulShutdown();
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error(chalk.red(`[Worker ${process.pid}] Uncaught Exception:`), error);
            this.sendToMaster({ 
                type: 'worker-error', 
                error: error.message,
                stack: error.stack 
            });
            this.emergencyShutdown();
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            console.error(chalk.red(`[Worker ${process.pid}] Unhandled Rejection:`), reason);
            this.sendToMaster({ 
                type: 'worker-error', 
                error: `Unhandled Rejection: ${reason}`,
                promise: promise.toString()
            });
            this.emergencyShutdown();
        });

        // Handle disconnect from master
        process.on('disconnect', () => {
            console.log(chalk.yellow(`[Worker ${process.pid}] Disconnected from master`));
            this.gracefulShutdown();
        });
    }

    startHealthMonitoring() {
        // Monitor worker health
        this.healthCheckInterval = setInterval(() => {
            const now = Date.now();
            const timeSinceLastCheck = now - this.lastHealthCheck;
            
            // If we haven't received a health check from master in too long, something might be wrong
            if (timeSinceLastCheck > 60000) { // 1 minute
                console.warn(chalk.yellow(`[Worker ${process.pid}] No health check from master for ${timeSinceLastCheck}ms`));
            }
            
            // Monitor memory usage
            const memUsage = process.memoryUsage();
            const memUsageMB = Math.round(memUsage.heapUsed / 1024 / 1024);
            
            if (memUsageMB > 500) { // 500MB threshold
                console.warn(chalk.yellow(`[Worker ${process.pid}] High memory usage: ${memUsageMB}MB`));
            }
            
        }, 30000); // Check every 30 seconds
    }

    sendToMaster(message) {
        if (process.send) {
            process.send(message);
        }
    }

    async gracefulShutdown() {
        if (this.isShuttingDown) {
            console.log(chalk.yellow(`[Worker ${process.pid}] Shutdown already in progress...`));
            return;
        }

        this.isShuttingDown = true;
        console.log(chalk.yellow(`[Worker ${process.pid}] Starting graceful shutdown...`));

        // Clear health monitoring
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }

        // Stop accepting new connections
        if (this.server) {
            this.server.close(() => {
                console.log(chalk.blue(`[Worker ${process.pid}] Server stopped accepting new connections`));
            });
        }

        // Close existing connections gracefully
        const closeConnections = () => {
            return new Promise((resolve) => {
                if (this.connections.size === 0) {
                    resolve();
                    return;
                }

                console.log(chalk.blue(`[Worker ${process.pid}] Closing ${this.connections.size} active connections...`));
                
                this.connections.forEach(socket => {
                    socket.end();
                });

                // Wait for connections to close
                const checkConnections = () => {
                    if (this.connections.size === 0) {
                        resolve();
                    } else {
                        setTimeout(checkConnections, 100);
                    }
                };
                checkConnections();
            });
        };

        // Set timeout for graceful shutdown
        const shutdownTimeout = setTimeout(() => {
            console.log(chalk.red(`[Worker ${process.pid}] Graceful shutdown timeout, forcing exit...`));
            this.emergencyShutdown();
        }, this.gracefulShutdownTimeout);

        try {
            // Wait for connections to close
            await closeConnections();
            
            console.log(chalk.green(`[Worker ${process.pid}] Graceful shutdown completed`));
            clearTimeout(shutdownTimeout);
            process.exit(0);
        } catch (error) {
            console.error(chalk.red(`[Worker ${process.pid}] Error during graceful shutdown:`), error);
            clearTimeout(shutdownTimeout);
            this.emergencyShutdown();
        }
    }

    emergencyShutdown() {
        console.log(chalk.red(`[Worker ${process.pid}] Emergency shutdown initiated...`));
        
        // Force close all connections
        this.connections.forEach(socket => {
            socket.destroy();
        });
        
        process.exit(1);
    }

    // Method to register the Express server for connection tracking
    registerServer(server) {
        this.server = server;
        this.trackServerConnections(server);
        
        console.log(chalk.green(`[Worker ${process.pid}] Server registered for connection tracking`));
    }

    // Health check endpoint handler
    getHealthStatus() {
        return {
            pid: process.pid,
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            connections: this.connections.size,
            isShuttingDown: this.isShuttingDown,
            lastHealthCheck: this.lastHealthCheck,
            timestamp: Date.now()
        };
    }

    // Middleware to add health check endpoint
    healthCheckMiddleware() {
        return (req, res, next) => {
            if (req.path === '/health' && req.method === 'GET') {
                const status = this.getHealthStatus();
                res.json({
                    status: 'healthy',
                    worker: status,
                    timestamp: new Date().toISOString()
                });
                return;
            }
            next();
        };
    }

    // Middleware to track requests
    requestTrackingMiddleware() {
        return (req, res, next) => {
            const startTime = Date.now();
            
            res.on('finish', () => {
                const duration = Date.now() - startTime;
                if (duration > 5000) { // Log slow requests (>5s)
                    console.warn(chalk.yellow(`[Worker ${process.pid}] Slow request: ${req.method} ${req.path} - ${duration}ms`));
                }
            });
            
            next();
        };
    }
}

// Only create worker instance if this is actually a worker process
let workerInstance = null;

if (cluster.isWorker) {
    workerInstance = new WorkerProcess();
}

module.exports = {
    WorkerProcess,
    getInstance: () => workerInstance
};
