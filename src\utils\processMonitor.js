require('dotenv').config();

const chalk = require('chalk');
const cluster = require('cluster');
const os = require('os');

class ProcessMonitor {
    constructor() {
        this.metrics = {
            startTime: Date.now(),
            requests: 0,
            errors: 0,
            memoryPeaks: [],
            cpuUsage: [],
            responseTimeHistory: []
        };
        
        this.thresholds = {
            memoryWarning: 500 * 1024 * 1024, // 500MB
            memoryCritical: 1024 * 1024 * 1024, // 1GB
            cpuWarning: 80, // 80%
            cpuCritical: 95, // 95%
            responseTimeWarning: 5000, // 5 seconds
            responseTimeCritical: 10000 // 10 seconds
        };
        
        this.startMonitoring();
    }

    startMonitoring() {
        // Monitor every 30 seconds
        setInterval(() => {
            this.collectMetrics();
            this.checkThresholds();
        }, 30000);

        // Detailed monitoring every 5 minutes
        setInterval(() => {
            this.detailedHealthCheck();
        }, 5 * 60 * 1000);
    }

    collectMetrics() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        // Store memory usage
        this.metrics.memoryPeaks.push({
            timestamp: Date.now(),
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss
        });

        // Store CPU usage
        this.metrics.cpuUsage.push({
            timestamp: Date.now(),
            user: cpuUsage.user,
            system: cpuUsage.system
        });

        // Keep only last 100 entries
        if (this.metrics.memoryPeaks.length > 100) {
            this.metrics.memoryPeaks = this.metrics.memoryPeaks.slice(-100);
        }
        
        if (this.metrics.cpuUsage.length > 100) {
            this.metrics.cpuUsage = this.metrics.cpuUsage.slice(-100);
        }
    }

    checkThresholds() {
        const memUsage = process.memoryUsage();
        const processType = cluster.isWorker ? `Worker ${process.pid}` : 'Master';

        // Memory threshold checks
        if (memUsage.heapUsed > this.thresholds.memoryCritical) {
            console.error(chalk.red(`[${processType}] CRITICAL: Memory usage ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB exceeds critical threshold`));
            this.triggerAlert('memory', 'critical', memUsage.heapUsed);
        } else if (memUsage.heapUsed > this.thresholds.memoryWarning) {
            console.warn(chalk.yellow(`[${processType}] WARNING: Memory usage ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB exceeds warning threshold`));
        }

        // Check for memory leaks (increasing trend)
        if (this.metrics.memoryPeaks.length >= 10) {
            const recent = this.metrics.memoryPeaks.slice(-10);
            const trend = this.calculateTrend(recent.map(m => m.heapUsed));
            
            if (trend > 0.1) { // 10% increase trend
                console.warn(chalk.yellow(`[${processType}] WARNING: Potential memory leak detected (increasing trend: ${(trend * 100).toFixed(2)}%)`));
            }
        }
    }

    calculateTrend(values) {
        if (values.length < 2) return 0;
        
        const first = values[0];
        const last = values[values.length - 1];
        
        return (last - first) / first;
    }

    detailedHealthCheck() {
        const processType = cluster.isWorker ? `Worker ${process.pid}` : 'Master';
        const uptime = process.uptime();
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();

        console.log(chalk.blue(`[${processType}] === Detailed Health Check ===`));
        console.log(chalk.blue(`[${processType}] Uptime: ${Math.round(uptime / 60)} minutes`));
        console.log(chalk.blue(`[${processType}] Memory - Heap: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB / ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`));
        console.log(chalk.blue(`[${processType}] Memory - RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`));
        console.log(chalk.blue(`[${processType}] Requests handled: ${this.metrics.requests}`));
        console.log(chalk.blue(`[${processType}] Errors: ${this.metrics.errors}`));
        
        if (this.metrics.responseTimeHistory.length > 0) {
            const avgResponseTime = this.metrics.responseTimeHistory.reduce((a, b) => a + b, 0) / this.metrics.responseTimeHistory.length;
            console.log(chalk.blue(`[${processType}] Avg Response Time: ${Math.round(avgResponseTime)}ms`));
        }

        // System-wide metrics (only for master or single process)
        if (cluster.isPrimary || !cluster.isWorker) {
            const systemMemory = os.totalmem();
            const freeMemory = os.freemem();
            const usedMemory = systemMemory - freeMemory;
            const memoryUsagePercent = (usedMemory / systemMemory) * 100;

            console.log(chalk.blue(`[${processType}] System Memory Usage: ${memoryUsagePercent.toFixed(2)}%`));
            console.log(chalk.blue(`[${processType}] System Load Average: ${os.loadavg().map(l => l.toFixed(2)).join(', ')}`));
        }
    }

    recordRequest(responseTime) {
        this.metrics.requests++;
        
        if (responseTime) {
            this.metrics.responseTimeHistory.push(responseTime);
            
            // Keep only last 1000 response times
            if (this.metrics.responseTimeHistory.length > 1000) {
                this.metrics.responseTimeHistory = this.metrics.responseTimeHistory.slice(-1000);
            }

            // Check response time thresholds
            const processType = cluster.isWorker ? `Worker ${process.pid}` : 'Process';
            
            if (responseTime > this.thresholds.responseTimeCritical) {
                console.error(chalk.red(`[${processType}] CRITICAL: Response time ${responseTime}ms exceeds critical threshold`));
                this.triggerAlert('response_time', 'critical', responseTime);
            } else if (responseTime > this.thresholds.responseTimeWarning) {
                console.warn(chalk.yellow(`[${processType}] WARNING: Response time ${responseTime}ms exceeds warning threshold`));
            }
        }
    }

    recordError(error) {
        this.metrics.errors++;
        const processType = cluster.isWorker ? `Worker ${process.pid}` : 'Process';
        console.error(chalk.red(`[${processType}] Error recorded:`, error.message || error));
    }

    triggerAlert(type, severity, value) {
        const alert = {
            timestamp: new Date().toISOString(),
            type,
            severity,
            value,
            process: cluster.isWorker ? `worker-${process.pid}` : 'master',
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage()
        };

        // In a real application, you would send this to a monitoring service
        console.error(chalk.red(`[ALERT] ${severity.toUpperCase()} ${type} alert:`, JSON.stringify(alert, null, 2)));

        // Send alert to master process if this is a worker
        if (cluster.isWorker && process.send) {
            process.send({
                type: 'alert',
                data: alert
            });
        }
    }

    getMetrics() {
        const memUsage = process.memoryUsage();
        const uptime = process.uptime();
        
        return {
            ...this.metrics,
            currentMemory: memUsage,
            uptime,
            pid: process.pid,
            isWorker: cluster.isWorker,
            timestamp: Date.now()
        };
    }

    // Middleware to track request metrics
    requestTrackingMiddleware() {
        return (req, res, next) => {
            const startTime = Date.now();
            
            // Override res.end to capture response time
            const originalEnd = res.end;
            res.end = (...args) => {
                const responseTime = Date.now() - startTime;
                this.recordRequest(responseTime);
                
                // Log slow requests
                if (responseTime > 1000) {
                    console.warn(chalk.yellow(`Slow request: ${req.method} ${req.path} - ${responseTime}ms`));
                }
                
                return originalEnd.apply(res, args);
            };
            
            next();
        };
    }

    // Middleware to track errors
    errorTrackingMiddleware() {
        return (err, req, res, next) => {
            this.recordError(err);
            next(err);
        };
    }

    // Get health status
    getHealthStatus() {
        const memUsage = process.memoryUsage();
        const uptime = process.uptime();
        
        // Determine health status based on thresholds
        let status = 'healthy';
        const issues = [];
        
        if (memUsage.heapUsed > this.thresholds.memoryCritical) {
            status = 'critical';
            issues.push('Memory usage critical');
        } else if (memUsage.heapUsed > this.thresholds.memoryWarning) {
            status = 'warning';
            issues.push('Memory usage high');
        }
        
        if (this.metrics.responseTimeHistory.length > 0) {
            const avgResponseTime = this.metrics.responseTimeHistory.reduce((a, b) => a + b, 0) / this.metrics.responseTimeHistory.length;
            
            if (avgResponseTime > this.thresholds.responseTimeCritical) {
                status = 'critical';
                issues.push('Response time critical');
            } else if (avgResponseTime > this.thresholds.responseTimeWarning) {
                if (status !== 'critical') status = 'warning';
                issues.push('Response time high');
            }
        }
        
        return {
            status,
            issues,
            uptime,
            memory: {
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
                rss: Math.round(memUsage.rss / 1024 / 1024)
            },
            requests: this.metrics.requests,
            errors: this.metrics.errors,
            pid: process.pid,
            timestamp: new Date().toISOString()
        };
    }
}

// Create singleton instance
const processMonitor = new ProcessMonitor();

module.exports = {
    ProcessMonitor,
    getInstance: () => processMonitor
};
