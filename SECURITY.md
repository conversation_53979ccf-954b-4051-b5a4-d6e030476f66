# Security Guide for GSP Invoice Service

## 🔒 Security Improvements Implemented

### Critical Security Fixes

1. **Authentication Bug Fixed**
   - Fixed undefined `token` variable in `authUtils.js`
   - Proper token validation now implemented

2. **Enhanced Authentication System**
   - JWT-based authentication with secure token generation
   - Password hashing with PBKDF2 and salt
   - Token expiration and refresh mechanisms
   - Multi-layer authentication validation

3. **Process Security**
   - Master-worker cluster architecture for isolation
   - Graceful shutdown and restart capabilities
   - Process monitoring and health checks
   - Memory leak detection and alerting

### Security Middleware Implemented

1. **Rate Limiting**
   - Different limits for auth, API, and general endpoints
   - IP-based tracking and blocking
   - Configurable thresholds

2. **Input Validation & Sanitization**
   - Request body validation with Joi schemas
   - XSS protection through input sanitization
   - SQL injection prevention
   - File upload restrictions

3. **Security Headers**
   - Content Security Policy (CSP)
   - HTTP Strict Transport Security (HSTS)
   - X-Frame-Options, X-Content-Type-Options
   - Cross-Origin Resource Sharing (CORS) configuration

4. **Session Security**
   - Secure session management
   - CSRF protection for state-changing operations
   - HttpOnly and Secure cookie flags
   - Session timeout and rotation

## 🚀 Deployment Modes

### Development Mode
```bash
# Single process mode
npm run dev

# Cluster mode for testing
npm run dev:cluster
```

### Production Mode
```bash
# Production cluster mode (recommended)
npm run start:production

# Manual cluster mode
npm run start:cluster
```

## 🔧 Configuration Security

### Environment Variables
Copy `.env.example` to `.env` and configure:

```bash
# Critical security settings
JWT_SECRET=your-super-secure-jwt-secret-minimum-64-characters
ENCRYPTION_KEY=your-encryption-key-here
SESSION_SECRET=your-session-secret-here

# Database credentials
DB_USERNAME=your-db-user
DB_PASSWORD=your-secure-db-password

# GSP API credentials
GSP_CLIENT_ID=your-gsp-client-id
GSP_CLIENT_SECRET=your-gsp-client-secret
```

### Security Checklist

- [ ] All environment variables configured
- [ ] Strong, unique secrets generated
- [ ] Database credentials secured
- [ ] HTTPS enabled in production
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers enabled
- [ ] Logging and monitoring active

## 🛡️ Security Features

### 1. Master Process Management
- **Isolation**: Each worker runs in isolated process
- **Fault Tolerance**: Worker crashes don't affect other workers
- **Zero Downtime**: Rolling restarts without service interruption
- **Resource Monitoring**: Memory and CPU usage tracking

### 2. Enhanced Authentication
```javascript
// JWT Authentication
const { authenticateJWT } = require('./src/utils/authUtils');
app.use('/api/protected', authenticateJWT);

// Legacy token validation (for backward compatibility)
const { validateToken } = require('./src/utils/authUtils');
app.use('/api/legacy', validateToken);
```

### 3. Rate Limiting
```javascript
// Different rate limits for different endpoints
app.use('/auth', authRateLimit);      // 5 requests per 15 minutes
app.use('/api', apiRateLimit);        // 30 requests per minute
app.use(generalRateLimit);            // 100 requests per 15 minutes
```

### 4. Input Validation
```javascript
const Joi = require('joi');
const { validateInput } = require('./src/middleware/securityMiddleware');

const schema = Joi.object({
    email: Joi.string().email().required(),
    companyUid: Joi.string().uuid().required()
});

app.post('/api/endpoint', validateInput(schema), handler);
```

## 📊 Monitoring & Alerting

### Health Checks
- **Endpoint**: `GET /health`
- **Worker Health**: Individual worker status
- **System Metrics**: Memory, CPU, uptime
- **Database Connectivity**: Connection status

### Process Monitoring
- Memory usage tracking and leak detection
- Response time monitoring
- Error rate tracking
- Worker restart monitoring

### Alerts
- Critical memory usage (>1GB)
- High response times (>10s)
- Worker restart failures
- Authentication failures

## 🔍 Security Logging

All security events are logged with appropriate levels:

```javascript
// Authentication events
console.log(chalk.green(`JWT Authentication successful for: ${user.email}`));
console.warn(chalk.yellow(`Invalid JWT token from IP: ${req.ip}`));

// Rate limiting
console.warn(chalk.yellow(`Rate limit exceeded for IP: ${req.ip}`));

// Process events
console.error(chalk.red(`Worker ${pid} crashed: ${error}`));
```

## 🚨 Incident Response

### Worker Crash
1. Automatic restart with exponential backoff
2. Alert logging with crash details
3. Maximum restart attempts (5 by default)
4. Graceful degradation if worker fails repeatedly

### Security Breach
1. Immediate rate limiting activation
2. Session invalidation
3. Alert generation
4. Detailed logging for forensics

### Memory Leaks
1. Automatic detection and alerting
2. Worker restart if critical threshold reached
3. Metrics collection for analysis
4. Graceful degradation

## 🔒 Best Practices

### 1. Environment Security
- Use strong, unique secrets for each environment
- Rotate secrets regularly
- Never commit secrets to version control
- Use environment-specific configurations

### 2. Network Security
- Enable HTTPS in production
- Configure proper CORS policies
- Use reverse proxy (nginx/Apache) for SSL termination
- Implement IP whitelisting where appropriate

### 3. Database Security
- Use connection pooling
- Implement query parameterization
- Regular security updates
- Database access logging

### 4. Monitoring
- Enable comprehensive logging
- Set up alerting for security events
- Regular security audits
- Performance monitoring

## 🔄 Updates and Maintenance

### Security Updates
```bash
# Check for vulnerabilities
npm audit

# Fix vulnerabilities
npm audit fix

# Update dependencies
npm update
```

### Zero-Downtime Deployment
```bash
# Send SIGUSR2 to reload workers
kill -USR2 <master-pid>

# Or use PM2 for production
pm2 reload cluster.js
```

## 📞 Support

For security issues or questions:
1. Check logs in `/logs` directory
2. Review health check endpoint `/health`
3. Monitor process metrics
4. Contact development team for critical issues

## 🔐 Security Compliance

This implementation follows:
- OWASP Top 10 security guidelines
- Node.js security best practices
- Express.js security recommendations
- Industry standard authentication patterns
