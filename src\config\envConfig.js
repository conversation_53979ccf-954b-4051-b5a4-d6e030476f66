const dotenv = require('dotenv');
const chalk = require('chalk');
const crypto = require('crypto');

dotenv.config();

// Validate required environment variables
const validateEnvVars = () => {
    const requiredVars = [
        'GSP_CLIENT_ID',
        'GSP_CLIENT_SECRET',
        'GSP_USER_NAME',
        'GSP_PASSWORD',
        'JWT_SECRET'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
        console.error(chalk.red(`[Config] Missing required environment variables: ${missing.join(', ')}`));
        if (process.env.NODE_ENV === 'production') {
            console.error(chalk.red('[Config] Cannot start in production without required environment variables'));
            process.exit(1);
        } else {
            console.warn(chalk.yellow('[Config] Using fallback values for development. DO NOT use in production!'));
        }
    }
};

// Generate secure random token if not provided
const generateSecureToken = () => {
    return crypto.randomBytes(32).toString('hex');
};

// Validate configuration
validateEnvVars();

let config = {
    gsp_client_id: process.env.GSP_CLIENT_ID || (process.env.NODE_ENV !== 'production' ? '79536E39F216449883720CCD53643D8F' : null),
    gsp_client_secret: process.env.GSP_CLIENT_SECRET || (process.env.NODE_ENV !== 'production' ? 'EE5EFAACG8434G43E8GA90EG9660E98C3D71' : null),
    gsp_user_name: process.env.GSP_USER_NAME || (process.env.NODE_ENV !== 'production' ? 'adqgsppjusr1' : null),
    gsp_password: process.env.GSP_PASSWORD || (process.env.NODE_ENV !== 'production' ? 'Gsp@1234' : null),
    gsp_base_url: process.env.GSP_GSP_BASE_URL || 'https://gsp.adaequare.com',
    based_token: process.env.BASIC_TOKEN || (process.env.NODE_ENV !== 'production' ? 'dGVzdDp0ZXN0' : generateSecureToken()),
    jwt_secret: process.env.JWT_SECRET || (process.env.NODE_ENV !== 'production' ? generateSecureToken() : null),
    encryption_key: process.env.ENCRYPTION_KEY || generateSecureToken(),
    session_secret: process.env.SESSION_SECRET || generateSecureToken(),
}
const envConfig = {
    production: {
        host_port: process.env.PORT || 5001,
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_DATABASE,
        host: process.env.DB_HOST || 'localhost',
        redis_host: process.env.REDIS_HOST,
        redis_port: process.env.REDIS_PORT || 6379,
        redis_password: process.env.REDIS_PASSWORD,
        redis_timeout: parseInt(process.env.REDIS_TIMEOUT) || 86400,
        dialect: 'mysql',
        // Security settings for production
        secure_cookies: true,
        trust_proxy: true,
        cors_origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : false,
        rate_limit_enabled: true,
        max_workers: process.env.MAX_WORKERS || require('os').cpus().length,
        ...config
    },
    development: {
        host_port: process.env.PORT || 5000,
        username: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || 'root',
        database: process.env.DB_DATABASE || 'invoicefp',
        host: process.env.DB_HOST || 'localhost',
        dialect: 'mysql',
        redis_host: process.env.REDIS_HOST || 'localhost',
        redis_port: process.env.REDIS_PORT || 6379,
        redis_timeout: parseInt(process.env.REDIS_TIMEOUT) || 86400,
        redis_password: process.env.REDIS_PASSWORD || '',
        // Security settings for development
        secure_cookies: false,
        trust_proxy: false,
        cors_origin: true, // Allow all origins in development
        rate_limit_enabled: false, // Disable rate limiting in development
        max_workers: 1, // Single worker for development
        ...config
    },
}

// Validate critical configuration
const validateConfig = (env) => {
    const envConfigObj = envConfig[env];

    if (!envConfigObj) {
        console.error(chalk.red(`[Config] Invalid environment: ${env}`));
        process.exit(1);
    }

    // Check for null values that should not be null
    const criticalFields = ['gsp_client_id', 'gsp_client_secret', 'jwt_secret'];
    const nullFields = criticalFields.filter(field => envConfigObj[field] === null);

    if (nullFields.length > 0) {
        console.error(chalk.red(`[Config] Critical configuration fields are null: ${nullFields.join(', ')}`));
        process.exit(1);
    }

    console.log(chalk.green(`[Config] Configuration validated for environment: ${env}`));
};

// Validate current environment
const currentEnv = process.env.HOST_ENV || process.env.NODE_ENV || 'development';
validateConfig(currentEnv);

module.exports = envConfig;