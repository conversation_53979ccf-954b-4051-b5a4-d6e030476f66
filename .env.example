# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development
HOST_ENV=development
PORT=5000

# =============================================================================
# CLUSTER CONFIGURATION
# =============================================================================
# Set to 'false' to disable clustering
CLUSTER_MODE=true
# Number of worker processes (defaults to CPU count)
WORKERS=4
# Maximum restart attempts per worker
MAX_RESTARTS=5
# Delay between worker restarts (ms)
RESTART_DELAY=5000
# Graceful shutdown timeout (ms)
SHUTDOWN_TIMEOUT=30000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=localhost
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=invoicefp

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_TIMEOUT=86400

# =============================================================================
# GSP API CONFIGURATION
# =============================================================================
# IMPORTANT: Replace these with your actual GSP credentials
GSP_CLIENT_ID=your_gsp_client_id_here
GSP_CLIENT_SECRET=your_gsp_client_secret_here
GSP_USER_NAME=your_gsp_username_here
GSP_PASSWORD=your_gsp_password_here
GSP_GSP_BASE_URL=https://gsp.adaequare.com

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CRITICAL: Generate secure random values for production
# Use: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# JWT Secret (REQUIRED for production)
JWT_SECRET=your_jwt_secret_here_minimum_32_characters

# Basic authentication token
BASIC_TOKEN=your_basic_token_here

# Encryption key for sensitive data
ENCRYPTION_KEY=your_encryption_key_here

# Session secret for Express sessions
SESSION_SECRET=your_session_secret_here

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins for production
# Example: https://yourdomain.com,https://api.yourdomain.com
CORS_ORIGIN=*

# =============================================================================
# RATE LIMITING
# =============================================================================
# Enable rate limiting (recommended for production)
RATE_LIMIT_ENABLED=true

# =============================================================================
# SSL/TLS CONFIGURATION (for production)
# =============================================================================
# SSL_KEY_PATH=/path/to/private-key.pem
# SSL_CERT_PATH=/path/to/certificate.pem
# SSL_CA_PATH=/path/to/ca-certificate.pem

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
# Log level: error, warn, info, debug
LOG_LEVEL=info

# Enable detailed request logging
REQUEST_LOGGING=true

# Enable performance monitoring
PERFORMANCE_MONITORING=true

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
# Health check endpoint path
HEALTH_CHECK_PATH=/health

# Health check interval (ms)
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# PRODUCTION SECURITY SETTINGS
# =============================================================================
# Trust proxy headers (set to true if behind reverse proxy)
TRUST_PROXY=false

# Use secure cookies (set to true for HTTPS)
SECURE_COOKIES=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Enable debug mode
DEBUG=false

# Enable hot reload for development
HOT_RELOAD=false

# =============================================================================
# EXAMPLE PRODUCTION VALUES
# =============================================================================
# Uncomment and modify for production deployment:

# NODE_ENV=production
# HOST_ENV=production
# PORT=5001
# CLUSTER_MODE=true
# WORKERS=8
# 
# DB_HOST=your-production-db-host
# DB_USERNAME=your-production-db-user
# DB_PASSWORD=your-secure-db-password
# DB_DATABASE=your-production-db-name
# 
# REDIS_HOST=your-production-redis-host
# REDIS_PORT=6379
# REDIS_PASSWORD=your-secure-redis-password
# 
# JWT_SECRET=your-super-secure-jwt-secret-at-least-64-characters-long
# BASIC_TOKEN=your-secure-basic-token
# ENCRYPTION_KEY=your-secure-encryption-key
# SESSION_SECRET=your-secure-session-secret
# 
# CORS_ORIGIN=https://yourdomain.com,https://api.yourdomain.com
# TRUST_PROXY=true
# SECURE_COOKIES=true
# RATE_LIMIT_ENABLED=true
# 
# LOG_LEVEL=warn
# REQUEST_LOGGING=false
# PERFORMANCE_MONITORING=true

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit .env files to version control
# 2. Use strong, unique secrets for each environment
# 3. Rotate secrets regularly
# 4. Use environment-specific configurations
# 5. Enable all security features in production
# 6. Monitor and log security events
# 7. Keep dependencies updated
# 8. Use HTTPS in production
# 9. Implement proper backup strategies
# 10. Regular security audits
