// ============================================================================
// GSP Invoice API Test Script
// ============================================================================
// This script tests all 4 GSP invoice API endpoints:
// 1. Create Invoice
// 2. Cancel Invoice  
// 3. Get Invoice by IRN
// 4. Get Invoice by Document Number
// ============================================================================

require('dotenv').config();

const chalk = require('chalk');
const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000';
const API_TOKEN = process.env.API_TOKEN || 'dGVzdDp0ZXN0'; // Default test token

// Test data
const testInvoiceData = {
    invoiceData: {
        voucherNumber: `TEST-INV-${Date.now()}`,
        voucherTime: new Date().toISOString().split('T')[0],
        fromFirm: {
            gstin: "29ABCDE1234F1Z5",
            name: "Test Seller Company Pvt Ltd",
            address: "123 Test Street, Business Area",
            state: "Karnataka",
            pincode: "560001",
            email: "<EMAIL>"
        },
        toFirm: {
            gstin: "27FGHIJ5678K2L9", 
            name: "Test Buyer Corporation Ltd",
            address: "456 Purchase Avenue, Industrial Zone",
            state: "Maharashtra",
            pincode: "400001",
            email: "<EMAIL>"
        },
        items: [
            {
                skuName: "Test Widget Premium",
                hsnCode: "8471",
                quantity: 5,
                rate: 2000.00,
                totalCost: 10000.00,
                gstRate: 18,
                unit: "PCS",
                isService: "N",
                cessRate: 0,
                discount: 0
            },
            {
                skuName: "Test Service Consultation",
                hsnCode: "9983",
                quantity: 1,
                rate: 5000.00,
                totalCost: 5000.00,
                gstRate: 18,
                unit: "HRS",
                isService: "Y",
                cessRate: 0,
                discount: 500
            }
        ],
        igstOnIntra: "N",
        discount: 100,
        otherCharges: 50,
        roundOff: 0
    }
};

// HTTP client configuration
const apiClient = axios.create({
    baseURL: BASE_URL,
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`,
        'company-uid': 'test-company-uid'
    }
});

// Test results storage
let testResults = {
    createInvoice: null,
    cancelInvoice: null,
    getInvoiceByIrn: null,
    getInvoiceByDocument: null
};

console.log(chalk.blue('='.repeat(80)));
console.log(chalk.blue('🧪 GSP Invoice API Test Suite'));
console.log(chalk.blue('='.repeat(80)));
console.log(chalk.green(`Base URL: ${BASE_URL}`));
console.log(chalk.green(`Test Token: ${API_TOKEN.substring(0, 10)}...`));
console.log(chalk.blue('='.repeat(80)));

// ============================================================================
// TEST 1: CREATE INVOICE
// ============================================================================

async function testCreateInvoice() {
    console.log(chalk.yellow('\n📝 Test 1: Create Invoice'));
    console.log(chalk.blue('-'.repeat(50)));
    
    try {
        console.log(chalk.blue('Sending create invoice request...'));
        
        const response = await apiClient.post('/api/invoice/create', testInvoiceData);
        
        if (response.status === 200 && response.data.success) {
            console.log(chalk.green('✅ Create Invoice: SUCCESS'));
            console.log(chalk.green(`   Invoice Number: ${testInvoiceData.invoiceData.voucherNumber}`));
            
            if (response.data.data && response.data.data.controlCode) {
                console.log(chalk.green(`   IRN: ${response.data.data.controlCode.substring(0, 20)}...`));
                console.log(chalk.green(`   Document Number: ${response.data.data.documentNumber}`));
                console.log(chalk.green(`   Status: ${response.data.data.status}`));
                
                // Store results for subsequent tests
                testResults.createInvoice = {
                    success: true,
                    irn: response.data.data.controlCode,
                    documentNumber: response.data.data.documentNumber,
                    voucherNumber: testInvoiceData.invoiceData.voucherNumber
                };
            } else {
                console.log(chalk.yellow('⚠️  Create Invoice: SUCCESS but missing expected data'));
                testResults.createInvoice = { success: true, data: response.data };
            }
        } else {
            console.log(chalk.red('❌ Create Invoice: FAILED'));
            console.log(chalk.red(`   Status: ${response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(response.data, null, 2)}`));
            testResults.createInvoice = { success: false, error: response.data };
        }
        
    } catch (error) {
        console.log(chalk.red('❌ Create Invoice: ERROR'));
        console.log(chalk.red(`   Error: ${error.message}`));
        if (error.response) {
            console.log(chalk.red(`   Status: ${error.response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(error.response.data, null, 2)}`));
        }
        testResults.createInvoice = { success: false, error: error.message };
    }
}

// ============================================================================
// TEST 2: CANCEL INVOICE
// ============================================================================

async function testCancelInvoice() {
    console.log(chalk.yellow('\n❌ Test 2: Cancel Invoice'));
    console.log(chalk.blue('-'.repeat(50)));
    
    if (!testResults.createInvoice?.success || !testResults.createInvoice?.irn) {
        console.log(chalk.yellow('⚠️  Skipping cancel test - no IRN from create test'));
        testResults.cancelInvoice = { success: false, error: 'No IRN available' };
        return;
    }
    
    try {
        const cancelData = {
            irn: testResults.createInvoice.irn,
            cancelReason: "1",
            cancelRemark: "Test cancellation - automated test",
            gstin: testInvoiceData.invoiceData.fromFirm.gstin
        };
        
        console.log(chalk.blue('Sending cancel invoice request...'));
        console.log(chalk.blue(`IRN: ${cancelData.irn.substring(0, 20)}...`));
        
        const response = await apiClient.post('/api/invoice/cancel', cancelData);
        
        if (response.status === 200 && response.data.success) {
            console.log(chalk.green('✅ Cancel Invoice: SUCCESS'));
            console.log(chalk.green(`   IRN: ${cancelData.irn.substring(0, 20)}...`));
            console.log(chalk.green(`   Cancel Date: ${response.data.data?.cancelDate || 'N/A'}`));
            testResults.cancelInvoice = { success: true, data: response.data };
        } else {
            console.log(chalk.red('❌ Cancel Invoice: FAILED'));
            console.log(chalk.red(`   Status: ${response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(response.data, null, 2)}`));
            testResults.cancelInvoice = { success: false, error: response.data };
        }
        
    } catch (error) {
        console.log(chalk.red('❌ Cancel Invoice: ERROR'));
        console.log(chalk.red(`   Error: ${error.message}`));
        if (error.response) {
            console.log(chalk.red(`   Status: ${error.response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(error.response.data, null, 2)}`));
        }
        testResults.cancelInvoice = { success: false, error: error.message };
    }
}

// ============================================================================
// TEST 3: GET INVOICE BY IRN
// ============================================================================

async function testGetInvoiceByIrn() {
    console.log(chalk.yellow('\n🔍 Test 3: Get Invoice by IRN'));
    console.log(chalk.blue('-'.repeat(50)));
    
    if (!testResults.createInvoice?.success || !testResults.createInvoice?.irn) {
        console.log(chalk.yellow('⚠️  Skipping get by IRN test - no IRN from create test'));
        testResults.getInvoiceByIrn = { success: false, error: 'No IRN available' };
        return;
    }
    
    try {
        const irn = testResults.createInvoice.irn;
        const requestData = {
            gstin: testInvoiceData.invoiceData.fromFirm.gstin
        };
        
        console.log(chalk.blue('Sending get invoice by IRN request...'));
        console.log(chalk.blue(`IRN: ${irn.substring(0, 20)}...`));
        
        const response = await apiClient.post(`/api/invoice/get/${irn}`, requestData);
        
        if (response.status === 200 && response.data.success) {
            console.log(chalk.green('✅ Get Invoice by IRN: SUCCESS'));
            console.log(chalk.green(`   IRN: ${irn.substring(0, 20)}...`));
            console.log(chalk.green(`   Document Number: ${response.data.data?.documentNumber || 'N/A'}`));
            console.log(chalk.green(`   Status: ${response.data.data?.status || 'N/A'}`));
            testResults.getInvoiceByIrn = { success: true, data: response.data };
        } else {
            console.log(chalk.red('❌ Get Invoice by IRN: FAILED'));
            console.log(chalk.red(`   Status: ${response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(response.data, null, 2)}`));
            testResults.getInvoiceByIrn = { success: false, error: response.data };
        }
        
    } catch (error) {
        console.log(chalk.red('❌ Get Invoice by IRN: ERROR'));
        console.log(chalk.red(`   Error: ${error.message}`));
        if (error.response) {
            console.log(chalk.red(`   Status: ${error.response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(error.response.data, null, 2)}`));
        }
        testResults.getInvoiceByIrn = { success: false, error: error.message };
    }
}

// ============================================================================
// TEST 4: GET INVOICE BY DOCUMENT NUMBER
// ============================================================================

async function testGetInvoiceByDocument() {
    console.log(chalk.yellow('\n📄 Test 4: Get Invoice by Document Number'));
    console.log(chalk.blue('-'.repeat(50)));
    
    if (!testResults.createInvoice?.success || !testResults.createInvoice?.voucherNumber) {
        console.log(chalk.yellow('⚠️  Skipping get by document test - no voucher number from create test'));
        testResults.getInvoiceByDocument = { success: false, error: 'No voucher number available' };
        return;
    }
    
    try {
        const documentNumber = testResults.createInvoice.voucherNumber;
        const requestData = {
            gstin: testInvoiceData.invoiceData.fromFirm.gstin
        };
        
        console.log(chalk.blue('Sending get invoice by document number request...'));
        console.log(chalk.blue(`Document Number: ${documentNumber}`));
        
        const response = await apiClient.post(`/api/invoice/document/${documentNumber}`, requestData);
        
        if (response.status === 200 && response.data.success) {
            console.log(chalk.green('✅ Get Invoice by Document: SUCCESS'));
            console.log(chalk.green(`   Document Number: ${documentNumber}`));
            console.log(chalk.green(`   IRN: ${response.data.data?.controlCode?.substring(0, 20) || 'N/A'}...`));
            console.log(chalk.green(`   Status: ${response.data.data?.status || 'N/A'}`));
            testResults.getInvoiceByDocument = { success: true, data: response.data };
        } else {
            console.log(chalk.red('❌ Get Invoice by Document: FAILED'));
            console.log(chalk.red(`   Status: ${response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(response.data, null, 2)}`));
            testResults.getInvoiceByDocument = { success: false, error: response.data };
        }
        
    } catch (error) {
        console.log(chalk.red('❌ Get Invoice by Document: ERROR'));
        console.log(chalk.red(`   Error: ${error.message}`));
        if (error.response) {
            console.log(chalk.red(`   Status: ${error.response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(error.response.data, null, 2)}`));
        }
        testResults.getInvoiceByDocument = { success: false, error: error.message };
    }
}

// ============================================================================
// TEST 5: HEALTH CHECK
// ============================================================================

async function testHealthCheck() {
    console.log(chalk.yellow('\n💓 Test 5: Health Check'));
    console.log(chalk.blue('-'.repeat(50)));
    
    try {
        console.log(chalk.blue('Sending health check request...'));
        
        const response = await apiClient.get('/api/invoice/health');
        
        if (response.status === 200 && response.data.status === 'healthy') {
            console.log(chalk.green('✅ Health Check: SUCCESS'));
            console.log(chalk.green(`   Service: ${response.data.service}`));
            console.log(chalk.green(`   Version: ${response.data.version}`));
            console.log(chalk.green(`   Endpoints: ${Object.keys(response.data.endpoints || {}).length} available`));
        } else {
            console.log(chalk.red('❌ Health Check: FAILED'));
            console.log(chalk.red(`   Status: ${response.status}`));
            console.log(chalk.red(`   Response: ${JSON.stringify(response.data, null, 2)}`));
        }
        
    } catch (error) {
        console.log(chalk.red('❌ Health Check: ERROR'));
        console.log(chalk.red(`   Error: ${error.message}`));
        if (error.response) {
            console.log(chalk.red(`   Status: ${error.response.status}`));
        }
    }
}

// ============================================================================
// RUN ALL TESTS
// ============================================================================

async function runAllTests() {
    try {
        // Test health check first
        await testHealthCheck();
        
        // Test create invoice
        await testCreateInvoice();
        
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test get by IRN
        await testGetInvoiceByIrn();
        
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test get by document number
        await testGetInvoiceByDocument();
        
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test cancel invoice (do this last as it changes invoice state)
        await testCancelInvoice();
        
        // Print summary
        printTestSummary();
        
    } catch (error) {
        console.error(chalk.red('Test suite failed:'), error);
    }
}

function printTestSummary() {
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue('📊 TEST SUMMARY'));
    console.log(chalk.blue('='.repeat(80)));
    
    const tests = [
        { name: 'Create Invoice', result: testResults.createInvoice },
        { name: 'Get Invoice by IRN', result: testResults.getInvoiceByIrn },
        { name: 'Get Invoice by Document', result: testResults.getInvoiceByDocument },
        { name: 'Cancel Invoice', result: testResults.cancelInvoice }
    ];
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
        if (test.result?.success) {
            console.log(chalk.green(`✅ ${test.name}: PASSED`));
            passed++;
        } else {
            console.log(chalk.red(`❌ ${test.name}: FAILED`));
            failed++;
        }
    });
    
    console.log(chalk.blue('-'.repeat(80)));
    console.log(chalk.green(`✅ Passed: ${passed}`));
    console.log(chalk.red(`❌ Failed: ${failed}`));
    console.log(chalk.blue(`📊 Total: ${tests.length}`));
    
    if (failed === 0) {
        console.log(chalk.green('\n🎉 All tests passed! GSP Invoice APIs are working correctly.'));
    } else {
        console.log(chalk.yellow('\n⚠️  Some tests failed. Check the logs above for details.'));
    }
    
    console.log(chalk.blue('='.repeat(80)));
}

// Run the test suite
runAllTests().catch(error => {
    console.error(chalk.red('Fatal error running test suite:'), error);
    process.exit(1);
});
