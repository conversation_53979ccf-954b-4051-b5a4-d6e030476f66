
// server.js
require('dotenv').config();

const cluster = require('cluster');
const chalk = require('chalk');

// Check if we should run in cluster mode
const shouldCluster = process.env.CLUSTER_MODE !== 'false' &&
                     (process.env.NODE_ENV === 'production' || process.env.FORCE_CLUSTER === 'true');

if (shouldCluster && cluster.isPrimary) {
    // Run master process
    console.log(chalk.blue('[Server] Starting in cluster mode...'));
    require('./src/cluster/masterProcess');
} else {
    // Run worker process or single process
    const express = require('express');
    const cors = require('cors');
    const bodyParser = require('body-parser');
    const session = require('express-session');
    const compression = require('compression');

    const env = process.env.HOST_ENV || process.env.NODE_ENV || 'development';
    const config = require('./src/config/envConfig')[env];

    // Import security middleware
    const {
        securityHeaders,
        generalRateLimit,
        // authRateLimit,
        apiRateLimit,
        sanitizeInput,
        securityLogger
    } = require('./src/middleware/securityMiddleware');

    // Import worker process utilities
    const { getInstance: getWorkerInstance } = require('./src/cluster/workerProcess');
    const workerInstance = getWorkerInstance();

    const app = express();

    // Trust proxy if configured
    if (config.trust_proxy) {
        app.set('trust proxy', 1);
    }

    // Compression middleware
    app.use(compression());

    // Security headers
    app.use(securityHeaders);

    // Security logging
    app.use(securityLogger);

    // Rate limiting (only in production or if explicitly enabled)
    if (config.rate_limit_enabled) {
        // app.use('/auth', authRateLimit);
        app.use('/api', apiRateLimit);
        app.use(generalRateLimit);
    }

    // CORS configuration
    app.use(cors({
        origin: config.cors_origin,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token']
    }));

    // Session configuration
    app.use(session({
        secret: config.session_secret,
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: config.secure_cookies,
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000 // 24 hours
        }
    }));

    // Body parsing with limits
    app.use(bodyParser.json({ limit: '10mb' }));
    app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

    // Input sanitization
    app.use(sanitizeInput);

    // Health check endpoint (before other routes)
    if (workerInstance) {
        app.use(workerInstance.healthCheckMiddleware());
        app.use(workerInstance.requestTrackingMiddleware());
    }

    // Basic health check for non-worker mode
    app.get('/health', (req, res) => {
        res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            environment: env,
            uptime: process.uptime(),
            memory: process.memoryUsage()
        });
    });

    // Root endpoint
    app.get('/', (req, res) => {
        res.json({
            message: 'Welcome to the Recibo Invoice Service',
            version: '2.0.0',
            environment: env,
            timestamp: new Date().toISOString()
        });
    });

    // TODO: Add your routes here
    // app.use('/api/gsp', require('./src/routes/gspRoutes'));
    // app.use('/api/invoice', require('./src/routes/invoiceRoutes'));

    // Error handling middleware
    app.use((err, req, res, next) => {
        console.error(chalk.red('[Server] Unhandled error:'), err);

        // Don't leak error details in production
        const isDevelopment = env === 'development';

        res.status(err.status || 500).json({
            error: 'Internal Server Error',
            message: isDevelopment ? err.message : 'Something went wrong',
            ...(isDevelopment && { stack: err.stack })
        });
    });

    // 404 handler
    app.use('*', (req, res) => {
        res.status(404).json({
            error: 'Not Found',
            message: `Route ${req.originalUrl} not found`,
            timestamp: new Date().toISOString()
        });
    });

    const port = config.host_port;
    const server = app.listen(port, () => {
        const processType = cluster.isWorker ? `Worker ${process.pid}` : 'Single Process';
        console.log(chalk.green(`[${processType}] Server listening on port ${port} in ${env} mode`));
        console.log(chalk.blue(`[${processType}] Configuration validated for environment: ${env}`));

        if (workerInstance) {
            workerInstance.registerServer(server);
        }
    });

    // Graceful shutdown handling
    const gracefulShutdown = (signal) => {
        console.log(chalk.yellow(`[Server] Received ${signal}, starting graceful shutdown...`));

        server.close(() => {
            console.log(chalk.green('[Server] HTTP server closed'));
            process.exit(0);
        });

        // Force shutdown after timeout
        setTimeout(() => {
            console.log(chalk.red('[Server] Forced shutdown due to timeout'));
            process.exit(1);
        }, 30000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
}