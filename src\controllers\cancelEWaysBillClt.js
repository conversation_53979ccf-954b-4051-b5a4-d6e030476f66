// ============================================================================
// Cancel E-Way Bill Controller with Country-wise Processing
// ============================================================================
// This controller handles E-Way Bill cancellation with:
// - Country-specific processing logic (similar to createInvoiceClt)
// - Company validation and lookup
// - Comprehensive input validation
// - Enhanced error handling and logging
// - Request tracking and monitoring
// ============================================================================

const gspController = require('./sub-controllers/gspController');
const database = require('../model/DBClient');
const logs = require('../utils/utils');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const { ProcessMonitor } = require('../utils/processMonitor');

// Get process monitor instance
const processMonitor = ProcessMonitor ? new ProcessMonitor() : null;

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

// Enhanced cancel E-Way Bill validation schema
const cancelEWaysBillValidationSchema = Joi.object({
    cancelData: Joi.object({
        // E-Way Bill Number - required for cancellation
        ewayBillNumber: Joi.string().required().length(12).pattern(/^[0-9]{12}$/).messages({
            'string.length': 'E-Way Bill number must be exactly 12 digits',
            'string.pattern.base': 'E-Way Bill number must contain only digits',
            'string.empty': 'E-Way Bill number is required for cancellation'
        }),

        // Cancel reason codes as per E-Way Bill rules
        cancelReason: Joi.string().required().valid('1', '2', '3', '4').messages({
            'any.only': 'Cancel reason must be: 1=Duplicate, 2=Data Entry Error, 3=Order Cancelled, 4=Others',
            'string.empty': 'Cancel reason is required'
        }),

        // Cancel remark/comment
        cancelRemark: Joi.string().required().min(1).max(100).messages({
            'string.empty': 'Cancel remark is required',
            'string.max': 'Cancel remark cannot exceed 100 characters',
            'string.min': 'Cancel remark must be at least 1 character'
        }),

        // GSTIN of the company cancelling the E-Way Bill
        gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)',
            'string.empty': 'GSTIN is required for E-Way Bill cancellation'
        }),

        // Optional tracking ID
        uid: Joi.string().uuid().optional()
    }).required()
});

// ============================================================================
// MAIN CONTROLLER FUNCTION
// ============================================================================

const cancelEWaysBillClt = async (req, res) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || uuidv4();

    try {
        // ========================================================================
        // STEP 1: REQUEST LOGGING AND VALIDATION
        // ========================================================================

        console.log(chalk.blue(`[Cancel E-Way Bill] Request ${requestId} started - IP: ${req.ip}`));

        // Validate input data
        const { error, value } = cancelEWaysBillValidationSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            console.warn(chalk.yellow(`[Cancel E-Way Bill] Validation failed for request ${requestId}:`, validationErrors));

            // Log validation failure
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'cancel_eway_bill',
                JSON.stringify(req.body),
                JSON.stringify({ error: 'Validation failed', details: validationErrors }),
                'VALIDATION_ERROR',
                0
            );

            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: validationErrors,
                requestId
            });
        }

        const { cancelData } = value;

        // Add request ID to cancel data for tracking
        cancelData.uid = cancelData.uid || requestId;

        // ========================================================================
        // STEP 2: COMPANY VALIDATION AND LOOKUP
        // ========================================================================

        // Get company from authenticated request (should be set by auth middleware)
        let company = req.company;

        // Fallback to header-based lookup (for backward compatibility)
        if (!company) {
            const companyUid = req.headers['company-uid'];

            if (!companyUid) {
                console.warn(chalk.yellow(`[Cancel E-Way Bill] No company UID provided for request ${requestId}`));
                return res.status(400).json({
                    success: false,
                    error: 'Company UID is required',
                    requestId
                });
            }

            company = await database.CompanyInfo.findOne({
                attributes: ['companyUid', 'country_name', 'country_code'],
                raw: true,
                where: { companyUid: companyUid }
            });
        }

        if (!company) {
            console.warn(chalk.yellow(`[Cancel E-Way Bill] Company not found for request ${requestId}`));

            // Log company not found
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'cancel_eway_bill',
                JSON.stringify({ companyUid: req.headers['company-uid'] }),
                JSON.stringify({ error: 'Company not found' }),
                'COMPANY_NOT_FOUND',
                0
            );

            return res.status(404).json({
                success: false,
                error: 'Company not found',
                requestId
            });
        }

        console.log(chalk.green(`[Cancel E-Way Bill] Processing cancellation for company ${company.companyUid} (${company.country_code}) - Request ${requestId}`));
        console.log(chalk.blue(`[Cancel E-Way Bill] E-Way Bill Number: ${cancelData.ewayBillNumber} - Reason: ${cancelData.cancelReason}`));

        // ========================================================================
        // STEP 3: COUNTRY-WISE PROCESSING
        // ========================================================================

        let response;
        let apiStatus = 1; // Success by default

        switch (company.country_code) {
            case 'IND':
                try {
                    console.log(chalk.blue(`[Cancel E-Way Bill] Processing Indian E-Way Bill cancellation for request ${requestId}`));

                    const gspResponse = await gspController.cancelGSPWayBill(cancelData);

                    if (gspResponse.success === false || gspResponse.error) {
                        console.error(chalk.red(`[Cancel E-Way Bill] GSP cancellation failed for request ${requestId}:`, gspResponse.error));
                        apiStatus = 0;

                        // Log GSP error
                        await logs.createApiLogs(
                            database,
                            req.user?.uid || 'anonymous',
                            'cancel_gsp_eway_bill',
                            JSON.stringify(cancelData),
                            JSON.stringify(gspResponse),
                            'GSP_ERROR',
                            0
                        );

                        return res.status(500).json({
                            success: false,
                            error: 'Failed to cancel GSP E-Way Bill',
                            details: process.env.NODE_ENV === 'development' ? gspResponse.error : 'Internal processing error',
                            requestId
                        });
                    }

                    response = {
                        success: true,
                        message: 'GSP E-Way Bill cancelled successfully',
                        data: gspResponse.data || gspResponse,
                        requestId,
                        timestamp: new Date().toISOString()
                    };

                    console.log(chalk.green(`[Cancel E-Way Bill] GSP E-Way Bill cancelled successfully for request ${requestId}`));

                } catch (gspError) {
                    console.error(chalk.red(`[Cancel E-Way Bill] GSP processing error for request ${requestId}:`, gspError));
                    apiStatus = 0;

                    // Log GSP processing error
                    await logs.createApiLogs(
                        database,
                        req.user?.uid || 'anonymous',
                        'cancel_gsp_eway_bill',
                        JSON.stringify(cancelData),
                        JSON.stringify({ error: gspError.message }),
                        'GSP_PROCESSING_ERROR',
                        0
                    );

                    return res.status(500).json({
                        success: false,
                        error: 'GSP processing failed',
                        details: process.env.NODE_ENV === 'development' ? gspError.message : 'Internal processing error',
                        requestId
                    });
                }
                break;

            case 'USA':
                console.warn(chalk.yellow(`[Cancel E-Way Bill] USA E-Way Bill cancellation not applicable for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    'cancel_eway_bill',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'E-Way Bill cancellation not applicable for USA' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'E-Way Bill cancellation is not applicable for USA',
                    details: 'E-Way Bill is specific to Indian GST system',
                    supportedCountries: ['IND'],
                    requestId
                });

            case 'CAN':
                console.warn(chalk.yellow(`[Cancel E-Way Bill] Canada E-Way Bill cancellation not applicable for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    'cancel_eway_bill',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'E-Way Bill cancellation not applicable for Canada' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'E-Way Bill cancellation is not applicable for Canada',
                    details: 'E-Way Bill is specific to Indian GST system',
                    supportedCountries: ['IND'],
                    requestId
                });

            default:
                console.warn(chalk.yellow(`[Cancel E-Way Bill] Unsupported country code ${company.country_code} for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    'cancel_eway_bill',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'Unsupported country code' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'Unsupported country code for E-Way Bill cancellation',
                    details: 'E-Way Bill cancellation is currently only supported for Indian companies',
                    supportedCountries: ['IND'],
                    currentCountry: company.country_code,
                    requestId
                });
        }

        // ========================================================================
        // STEP 4: SUCCESS LOGGING AND RESPONSE
        // ========================================================================

        // Log successful operation
        await logs.createApiLogs(
            database,
            req.user?.uid || 'anonymous',
            'cancel_eway_bill',
            JSON.stringify(cancelData),
            JSON.stringify(response),
            'SUCCESS',
            apiStatus
        );

        // Record metrics
        if (processMonitor) {
            const responseTime = Date.now() - startTime;
            processMonitor.recordRequest(responseTime);
        }

        console.log(chalk.green(`[Cancel E-Way Bill] Request ${requestId} completed successfully in ${Date.now() - startTime}ms`));

        return res.status(200).json(response);

    } catch (error) {
        console.error(chalk.red(`[Cancel E-Way Bill] Unexpected error for request ${requestId}:`), error);

        // Record error metrics
        if (processMonitor) {
            processMonitor.recordError(error);
        }

        // Log unexpected error
        try {
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'cancel_eway_bill',
                JSON.stringify(req.body),
                JSON.stringify({ error: error.message, stack: error.stack }),
                'UNEXPECTED_ERROR',
                0
            );
        } catch (logError) {
            console.error(chalk.red(`[Cancel E-Way Bill] Failed to log error for request ${requestId}:`), logError);
        }

        return res.status(500).json({
            success: false,
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
            requestId,
            timestamp: new Date().toISOString()
        });
    }
};

// ============================================================================
// MODULE EXPORTS
// ============================================================================

module.exports = {
    cancelEWaysBillClt,
    cancelEWaysBillValidationSchema
};