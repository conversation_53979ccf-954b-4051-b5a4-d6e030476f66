// Simple test to verify the server improvements
require('dotenv').config();

const chalk = require('chalk');

console.log(chalk.blue('='.repeat(60)));
console.log(chalk.blue('🧪 Testing GSP Invoice Service Improvements'));
console.log(chalk.blue('='.repeat(60)));

// Test 1: Configuration Loading
console.log(chalk.yellow('Test 1: Configuration Loading'));
try {
    const env = process.env.HOST_ENV || 'development';
    console.log(chalk.green(`✅ Environment: ${env}`));
    
    // Test config loading without validation
    const envConfig = require('./src/config/envConfig');
    console.log(chalk.green('✅ Configuration loaded successfully'));
    
    // Check if development config exists
    if (envConfig.development) {
        console.log(chalk.green('✅ Development configuration found'));
        console.log(chalk.blue(`   Port: ${envConfig.development.host_port}`));
        console.log(chalk.blue(`   Database: ${envConfig.development.database}`));
    }
    
} catch (error) {
    console.log(chalk.red(`❌ Configuration test failed: ${error.message}`));
}

// Test 2: Security Middleware Loading
console.log(chalk.yellow('\nTest 2: Security Middleware Loading'));
try {
    const securityMiddleware = require('./src/middleware/securityMiddleware');
    console.log(chalk.green('✅ Security middleware loaded'));
    
    if (typeof securityMiddleware.securityHeaders === 'function') {
        console.log(chalk.green('✅ Security headers middleware available'));
    }
    
    if (typeof securityMiddleware.generalRateLimit === 'function') {
        console.log(chalk.green('✅ Rate limiting middleware available'));
    }
    
} catch (error) {
    console.log(chalk.red(`❌ Security middleware test failed: ${error.message}`));
}

// Test 3: Authentication Utils
console.log(chalk.yellow('\nTest 3: Authentication Utils'));
try {
    const authUtils = require('./src/utils/authUtils');
    console.log(chalk.green('✅ Authentication utilities loaded'));
    
    if (typeof authUtils.validateToken === 'function') {
        console.log(chalk.green('✅ Token validation function available'));
    }
    
    if (typeof authUtils.generateJWT === 'function') {
        console.log(chalk.green('✅ JWT generation function available'));
    }
    
    if (typeof authUtils.hashPassword === 'function') {
        console.log(chalk.green('✅ Password hashing function available'));
    }
    
} catch (error) {
    console.log(chalk.red(`❌ Authentication utils test failed: ${error.message}`));
}

// Test 4: Process Monitor
console.log(chalk.yellow('\nTest 4: Process Monitor'));
try {
    const { ProcessMonitor } = require('./src/utils/processMonitor');
    console.log(chalk.green('✅ Process monitor loaded'));
    
    const monitor = new ProcessMonitor();
    if (monitor.getHealthStatus) {
        const health = monitor.getHealthStatus();
        console.log(chalk.green('✅ Health status check working'));
        console.log(chalk.blue(`   Status: ${health.status}`));
        console.log(chalk.blue(`   Memory: ${health.memory.heapUsed}MB`));
    }
    
} catch (error) {
    console.log(chalk.red(`❌ Process monitor test failed: ${error.message}`));
}

// Test 5: Cluster Components
console.log(chalk.yellow('\nTest 5: Cluster Components'));
try {
    const { WorkerProcess } = require('./src/cluster/workerProcess');
    console.log(chalk.green('✅ Worker process class loaded'));
    
    // Test master process loading (without running)
    const fs = require('fs');
    if (fs.existsSync('./src/cluster/masterProcess.js')) {
        console.log(chalk.green('✅ Master process file exists'));
    }
    
    if (fs.existsSync('./cluster.js')) {
        console.log(chalk.green('✅ Cluster manager file exists'));
    }
    
} catch (error) {
    console.log(chalk.red(`❌ Cluster components test failed: ${error.message}`));
}

// Test 6: Dependencies Check
console.log(chalk.yellow('\nTest 6: Dependencies Check'));
try {
    const requiredDeps = [
        'express',
        'express-rate-limit',
        'express-session',
        'compression',
        'validator',
        'jsonwebtoken',
        'joi'
    ];
    
    for (const dep of requiredDeps) {
        try {
            require(dep);
            console.log(chalk.green(`✅ ${dep} available`));
        } catch (err) {
            console.log(chalk.red(`❌ ${dep} missing`));
        }
    }
    
} catch (error) {
    console.log(chalk.red(`❌ Dependencies test failed: ${error.message}`));
}

console.log(chalk.blue('\n' + '='.repeat(60)));
console.log(chalk.green('🎉 Test completed! Check results above.'));
console.log(chalk.blue('='.repeat(60)));

// Test 7: Simple Express Server Test
console.log(chalk.yellow('\nTest 7: Simple Express Server'));
try {
    const express = require('express');
    const app = express();
    
    app.get('/test', (req, res) => {
        res.json({ message: 'Test endpoint working', timestamp: new Date().toISOString() });
    });
    
    const server = app.listen(0, () => {
        const port = server.address().port;
        console.log(chalk.green(`✅ Express server started on port ${port}`));
        
        // Make a test request
        const http = require('http');
        const options = {
            hostname: 'localhost',
            port: port,
            path: '/test',
            method: 'GET'
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log(chalk.green('✅ Test endpoint responded correctly'));
                    console.log(chalk.blue(`   Response: ${response.message}`));
                } catch (err) {
                    console.log(chalk.red('❌ Invalid response from test endpoint'));
                }
                server.close();
                console.log(chalk.green('✅ Test server closed'));
            });
        });
        
        req.on('error', (err) => {
            console.log(chalk.red(`❌ Test request failed: ${err.message}`));
            server.close();
        });
        
        req.end();
    });
    
} catch (error) {
    console.log(chalk.red(`❌ Express server test failed: ${error.message}`));
}
