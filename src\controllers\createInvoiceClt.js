const gspController = require('./sub-controllers/gspController');
const database = require('../model/DBClient');
const logs = require('../utils/utils');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const { ProcessMonitor } = require('../utils/processMonitor');

// Get process monitor instance
const processMonitor = ProcessMonitor ? new ProcessMonitor() : null;

// Input validation schema
const invoiceValidationSchema = Joi.object({
    invoiceData: Joi.object({
        voucherNumber: Joi.string().required().min(1).max(50),
        voucherTime: Joi.string().required(),
        fromFirm: Joi.object({
            gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/),
            name: Joi.string().required().min(1).max(200),
            address: Joi.string().required().min(1).max(500),
            state: Joi.string().required().min(1).max(100),
            pincode: Joi.string().required().pattern(/^[0-9]{6}$/),
            email: Joi.string().email().optional()
        }).required(),
        toFirm: Joi.object({
            gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/),
            name: Joi.string().required().min(1).max(200),
            address: Joi.string().required().min(1).max(500),
            state: Joi.string().required().min(1).max(100),
            pincode: Joi.string().required().pattern(/^[0-9]{6}$/),
            email: Joi.string().email().optional()
        }).required(),
        items: Joi.array().items(
            Joi.object({
                skuName: Joi.string().required().min(1).max(200),
                hsnCode: Joi.string().required().pattern(/^[0-9]{4,8}$/),
                quantity: Joi.number().positive().required(),
                rate: Joi.number().positive().required(),
                totalCost: Joi.number().positive().required(),
                gstRate: Joi.number().min(0).max(28).required(),
                unit: Joi.string().optional().default('PCS'),
                isService: Joi.string().valid('Y', 'N').optional().default('N'),
                cessRate: Joi.number().min(0).optional().default(0),
                discount: Joi.number().min(0).optional().default(0)
            })
        ).min(1).required(),
        uid: Joi.string().uuid().optional(),
        igstOnIntra: Joi.string().valid('Y', 'N').optional().default('N'),
        discount: Joi.number().min(0).optional().default(0),
        otherCharges: Joi.number().min(0).optional().default(0),
        roundOff: Joi.number().optional().default(0)
    }).required()
});

const createInvoiceClt = async (req, res) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || uuidv4();

    try {
        // Log request start
        console.log(chalk.blue(`[Invoice] Request ${requestId} started - IP: ${req.ip}`));

        // Validate input
        const { error, value } = invoiceValidationSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            console.warn(chalk.yellow(`[Invoice] Validation failed for request ${requestId}:`, validationErrors));

            // Log validation failure
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'create_invoice',
                JSON.stringify(req.body),
                JSON.stringify({ error: 'Validation failed', details: validationErrors }),
                'VALIDATION_ERROR',
                0
            );

            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors,
                requestId
            });
        }

        const { invoiceData } = value;

        // Add request ID to invoice data for tracking
        invoiceData.uid = invoiceData.uid || requestId;

        // Get company from authenticated request (should be set by auth middleware)
        let company = req.company;

        // Fallback to header-based lookup (for backward compatibility)
        if (!company) {
            const companyUid = req.headers['company-uid'];

            if (!companyUid) {
                console.warn(chalk.yellow(`[Invoice] No company UID provided for request ${requestId}`));
                return res.status(400).json({
                    error: 'Company UID is required',
                    requestId
                });
            }

            company = await database.CompanyInfo.findOne({
                attributes: ['companyUid', 'country_name', 'country_code'],
                raw: true,
                where: { companyUid: companyUid }
            });
        }

        if (!company) {
            console.warn(chalk.yellow(`[Invoice] Company not found for request ${requestId}`));

            // Log company not found
            await logs.createApiLogs(
                database,
                req.user?.uid ,
                'create_invoice',
                JSON.stringify({ companyUid: req.headers['company-uid'] }),
                JSON.stringify({ error: 'Company not found' }),
                'COMPANY_NOT_FOUND',
                0
            );

            return res.status(404).json({
                error: 'Company not found',
                requestId
            });
        }

        console.log(chalk.green(`[Invoice] Processing invoice for company ${company.companyUid} (${company.country_code}) - Request ${requestId}`));

        let response;
        let apiStatus = 0; // Success by default

        switch (company.country_code) {
            case 'IND':
                try {
                    const gspResponse = await gspController.createGSPInvoice(invoiceData);

                    if (gspResponse.error) {
                        console.error(chalk.red(`[Invoice] GSP invoice creation failed for request ${requestId}:`, gspResponse.error));
                        apiStatus = 1;

                        // Log GSP error
                        await logs.createApiLogs(
                            database,
                            req.user?.uid,
                            'gsp_create_invoice',
                            JSON.stringify(invoiceData),
                            JSON.stringify(gspResponse),
                            'INVOICE CREATION',
                            'GSP',
                            '/invoice/create',
                            apiStatus,

                        );

                        return res.status(500).json({
                            error: 'Failed to create GSP invoice',
                            details: process.env.NODE_ENV === 'development' ? gspResponse.error : 'Internal processing error',
                            requestId
                        });
                    } else {
                        await logs.createApiLogs(
                            database,
                            req.user?.uid,
                            'gsp_create_invoice',
                            JSON.stringify(invoiceData),
                            JSON.stringify(gspResponse),
                            'INVOICE CREATED',
                            'GSP',
                            '/invoice/create',
                            apiStatus,
                        );
                    }

                    response = {
                        message: 'GSP Invoice created successfully',
                        data: gspResponse,
                        requestId,
                        timestamp: new Date().toISOString()
                    };

                    console.log(chalk.green(`[Invoice] GSP invoice created successfully for request ${requestId}`));

                } catch (gspError) {
                    console.error(chalk.red(`[Invoice] GSP processing error for request ${requestId}:`, gspError));
                    apiStatus = 0;

                    // Log GSP processing error
                    await logs.createApiLogs(
                        database,
                        req.user?.uid || 'anonymous',
                        'create_gsp_invoice',
                        JSON.stringify(invoiceData),
                        JSON.stringify({ error: gspError.message }),
                        'GSP_PROCESSING_ERROR',
                        0
                    );

                    return res.status(500).json({
                        error: 'GSP processing failed',
                        details: process.env.NODE_ENV === 'development' ? gspError.message : 'Internal processing error',
                        requestId
                    });
                }
                break;

            default:
                console.warn(chalk.yellow(`[Invoice] Unsupported country code ${company.country_code} for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid,
                    '/invoice/create',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'Unsupported country code' }),
                    'UNSUPPORTED_COUNTRY',
                    'INVOICE CREATION',
                    '',
                    1
                );

                return res.status(400).json({
                    error: 'Unsupported country code for invoice creation',
                    supportedCountries: ['IND'],
                    requestId
                });
        }

        // Log successful operation
        await logs.createApiLogs(
            database,
            req.user?.uid || 'anonymous',
            'create_invoice',
            JSON.stringify(invoiceData),
            JSON.stringify(response),
            'SUCCESS',
            apiStatus
        );

        // Record metrics
        if (processMonitor) {
            const responseTime = Date.now() - startTime;
            processMonitor.recordRequest(responseTime);
        }

        console.log(chalk.green(`[Invoice] Request ${requestId} completed successfully in ${Date.now() - startTime}ms`));

        return res.status(200).json(response);

    } catch (error) {
        console.error(chalk.red(`[Invoice] Unexpected error for request ${requestId}:`), error);

        // Record error metrics
        if (processMonitor) {
            processMonitor.recordError(error);
        }

        // Log unexpected error
        try {
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'create_invoice',
                JSON.stringify(req.body),
                JSON.stringify({ error: error.message, stack: error.stack }),
                'UNEXPECTED_ERROR',
                0
            );
        } catch (logError) {
            console.error(chalk.red(`[Invoice] Failed to log error for request ${requestId}:`), logError);
        }

        return res.status(500).json({
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
            requestId,
            timestamp: new Date().toISOString()
        });
    }
}


module.exports = {
    createInvoiceClt
};