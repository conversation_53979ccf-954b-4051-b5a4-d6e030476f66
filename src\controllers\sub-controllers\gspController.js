// ============================================================================
// GSP (Goods and Services Tax Suvidha Provider) Controller
// ============================================================================
// This controller handles all GSP API interactions including:
// - Authentication and token management
// - Invoice creation, cancellation, and retrieval
// - E-Way Bill operations
// - Tax payer details lookup
// ============================================================================

const axios = require('axios');
const { redisSet, redisGet } = require('../../utils/redisUtils');
const axiosUtils = require('../../utils/axiosUtils');
const chalk = require('chalk');
const taxUtils = require('../../utils/taxUtils');
const { v4: uuidv4 } = require('uuid');

require('dotenv').config();

// ============================================================================
// CONFIGURATION AND CONSTANTS
// ============================================================================

const env = process.env.HOST_ENV || process.env.NODE_ENV || 'development';
const gspConfig = require('../../config/envConfig')[env];

// Validate GSP configuration on startup
const validateGspConfig = () => {
    const requiredFields = ['gsp_client_id', 'gsp_client_secret', 'gsp_user_name', 'gsp_password', 'gsp_base_url'];
    const missingFields = requiredFields.filter(field => !gspConfig[field]);

    if (missingFields.length > 0) {
        console.error(chalk.red(`[GSP Config] Missing required fields: ${missingFields.join(', ')}`));
        throw new Error(`Missing GSP configuration: ${missingFields.join(', ')}`);
    }

    console.log(chalk.green('[GSP Config] Configuration validated successfully'));
};

// Validate configuration on module load
try {
    validateGspConfig();
} catch (error) {
    console.error(chalk.red('[GSP Config] Configuration validation failed:', error.message));
}

// Build base URL with environment-specific path
let local_basedUrl = gspConfig.gsp_base_url;
if (env === 'development') {
    local_basedUrl = local_basedUrl + '/test';
    console.log(chalk.blue(`[GSP Config] Using development URL: ${local_basedUrl}`));
}

// Constants for better maintainability
const GSP_CONSTANTS = {
    // Redis keys for token storage
    REDIS_KEYS: {
        ACCESS_TOKEN: 'GSP_ACCESS_TOKEN',
        RESPONSE: 'GSP_RESPONSE'
    },

    // Timeout configurations
    TIMEOUTS: {
        REQUEST: 30000,        // 30 seconds for API requests
        TOKEN_REFRESH: 5000    // 5 seconds for token refresh
    },

    // Retry configurations
    RETRY: {
        MAX_ATTEMPTS: 3,       // Maximum retry attempts
        DELAY: 1000           // Base delay between retries (1 second)
    },

    // GSP API endpoints
    ENDPOINTS: {
        AUTHENTICATE: '/gsp/authenticate',
        INVOICE: '/enriched/ei/api/invoice',
        INVOICE_CANCEL: '/enriched/ei/api/invoice/cancel',
        INVOICE_GET: '/enriched/ei/api/invoice/irn',
        TAXPAYER: '/enriched/ei/api/master/gstin',
        EWAY_BILL: '/enriched/ei/api/ewaybill',
        EWAY_BILL_CANCEL: '/enriched/ei/api/ewaybill/cancel',
        EWAY_BILL_GET: '/enriched/ei/api/ewaybill'
    }
};

// ============================================================================
// AUTHENTICATION FUNCTIONS
// ============================================================================

/**
 * Authenticate with GSP service and obtain access token
 * @param {string} requestId - Optional request ID for tracking
 * @returns {Object} Authentication result with success status and token
 */
const gspLogin = async (requestId = null) => {
    const logPrefix = `[GSP Login${requestId ? ` ${requestId}` : ''}]`;

    try {
        console.log(chalk.blue(`${logPrefix} Attempting GSP authentication...`));

        // Make authentication request to GSP
        const response = await axiosUtils.globalSendPostRequest(
            {
                'gspappid': gspConfig.gsp_client_id,
                'gspappsecret': gspConfig.gsp_client_secret,
            },
            gspConfig.gsp_base_url,
            GSP_CONSTANTS.ENDPOINTS.AUTHENTICATE,
            null,
            GSP_CONSTANTS.TIMEOUTS.REQUEST
        );

        // Validate response structure
        if (!response?.data) {
            throw new Error('Invalid response from GSP authentication service');
        }

        const { access_token, expires_in } = response.data;

        // Validate required fields in response
        if (!access_token || !expires_in) {
            console.error(chalk.red(`${logPrefix} Missing access_token or expires_in in response`));
            throw new Error('Invalid authentication response: missing token or expiry');
        }

        // Calculate safe expiry time (reduce by 5 minutes for safety)
        const safeExpiryTime = Math.max(expires_in - 300, 300); // At least 5 minutes

        // Store token and response in Redis with expiry
        await Promise.all([
            redisSet(GSP_CONSTANTS.REDIS_KEYS.RESPONSE, JSON.stringify(response.data), safeExpiryTime),
            redisSet(GSP_CONSTANTS.REDIS_KEYS.ACCESS_TOKEN, access_token, safeExpiryTime)
        ]);

        console.log(chalk.green(`${logPrefix} Authentication successful, token expires in ${safeExpiryTime}s`));

        return {
            success: true,
            message: 'GSP Login Successful',
            access_token,
            expires_in: safeExpiryTime
        };

    } catch (error) {
        console.error(chalk.red(`${logPrefix} Authentication failed:`), error.message);

        return {
            success: false,
            error: error.message || 'GSP authentication failed',
            details: error.response?.data || null
        };
    }
};

/**
 * Get valid access token with automatic refresh if needed
 * @param {string} requestId - Optional request ID for tracking
 * @returns {Object} Token result with success status and access token
 */
const getValidAccessToken = async (requestId = null) => {
    const logPrefix = `[GSP Token${requestId ? ` ${requestId}` : ''}]`;

    try {
        // Try to get cached token from Redis
        let accessToken = await redisGet(GSP_CONSTANTS.REDIS_KEYS.ACCESS_TOKEN);

        if (accessToken) {
            console.log(chalk.blue(`${logPrefix} Using cached access token`));
            return { success: true, access_token: accessToken };
        }

        // No cached token found, need to refresh
        console.warn(chalk.yellow(`${logPrefix} No cached token found, refreshing...`));

        const loginResponse = await gspLogin(requestId);

        if (!loginResponse.success) {
            return {
                success: false,
                error: 'Failed to obtain access token',
                details: loginResponse.error
            };
        }

        return {
            success: true,
            access_token: loginResponse.access_token
        };

    } catch (error) {
        console.error(chalk.red(`${logPrefix} Token retrieval failed:`), error);
        return {
            success: false,
            error: error.message || 'Token retrieval failed'
        };
    }
};


// ============================================================================
// INVOICE MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Create GSP Invoice with comprehensive validation and error handling
 * @param {Object} data - Invoice data containing all required fields
 * @returns {Object} Invoice creation result with success status and data
 */
const createGSPInvoice = async (data) => {
    const requestId = data.uid || uuidv4();
    const logPrefix = `[GSP Invoice ${requestId}]`;

    try {
        console.log(chalk.blue(`${logPrefix} Starting invoice creation process...`));

        // ========================================================================
        // STEP 1: VALIDATE INPUT DATA
        // ========================================================================

        // Validate required firm details
        const fromCustomer = data.fromFirm;
        const toCustomer = data.toFirm;

        if (!fromCustomer || !toCustomer) {
            console.error(chalk.red(`${logPrefix} Missing firm details`));
            return {
                success: false,
                error: 'Missing required firm details',
                details: 'Both fromFirm and toFirm are required'
            };
        }

        // Validate items array
        if (!Array.isArray(data.items) || data.items.length === 0) {
            console.error(chalk.red(`${logPrefix} Invalid or empty items list`));
            return {
                success: false,
                error: 'Invalid or empty items list',
                details: 'At least one item is required for invoice creation'
            };
        }

        // ========================================================================
        // STEP 2: GET VALID ACCESS TOKEN
        // ========================================================================

        const tokenResponse = await getValidAccessToken(requestId);
        if (!tokenResponse.success) {
            console.error(chalk.red(`${logPrefix} Authentication failed`));
            return {
                success: false,
                error: 'Authentication failed',
                details: tokenResponse.error
            };
        }

        const accessToken = tokenResponse.access_token;

        // ========================================================================
        // STEP 3: CALCULATE TAX VALUES
        // ========================================================================

        console.log(chalk.blue(`${logPrefix} Calculating tax values...`));

        const invTaxVal = taxUtils.generateInvoiceValDtls({
            items: data.items,
            sellerGstin: fromCustomer.gstin,
            buyerGstin: toCustomer.gstin,
            otherCharges: data.otherCharges || 0,
            roundOff: data.roundOff || 0,
        });

        // ========================================================================
        // STEP 4: BUILD GSP REQUEST DATA STRUCTURE
        // ========================================================================

        console.log(chalk.blue(`${logPrefix} Building GSP request data structure...`));

        const gspRequestData = {
            Version: "1.1",

            // Transaction Details
            TranDtls: {
                TaxSch: "GST",                          // Tax Scheme: GST
                SupTyp: "B2B",                          // Supply Type: Business to Business
                RegRev: "N",                            // Reverse Charge: No
                EcmGstin: null,                         // E-commerce GSTIN: Not applicable
                IgstOnIntra: data.igstOnIntra || "N",   // IGST on Intra State: Default No
            },

            // Document Details
            DocDtls: {
                Typ: "INV",                             // Document Type: Invoice
                No: data.voucherNumber,                 // Invoice Number
                Dt: data.voucherTime,                   // Invoice Date
            },

            // Seller Details (From Firm)
            SellerDtls: {
                Gstin: fromCustomer.gstin,              // Seller GSTIN
                LglNm: fromCustomer.name,               // Legal Name
                TrdNm: fromCustomer.name,               // Trade Name
                Addr1: fromCustomer.address,            // Address Line 1
                Addr2: "",                              // Address Line 2 (optional)
                Loc: fromCustomer.state,                // Location/State
                Pin: fromCustomer.pincode,              // PIN Code
                Stcd: fromCustomer.gstin.substring(0, 2), // State Code (first 2 digits of GSTIN)
                Em: fromCustomer.email || ""            // Email (optional)
            },

            // Buyer Details (To Firm)
            BuyerDtls: {
                Gstin: toCustomer.gstin,                // Buyer GSTIN
                LglNm: toCustomer.name,                 // Legal Name
                Pos: toCustomer.gstin.substring(0, 2),  // Place of Supply (state code)
                Addr1: toCustomer.address,              // Address Line 1
                Addr2: "",                              // Address Line 2 (optional)
                Loc: toCustomer.state,                  // Location/State
                Pin: toCustomer.pincode,                // PIN Code
                Stcd: toCustomer.gstin.substring(0, 2), // State Code
                Em: toCustomer.email || ""              // Email (optional)
            },

            // Dispatch Details (same as seller for most cases)
            DispDtls: {
                Nm: fromCustomer.name,                  // Dispatch Name
                Addr1: fromCustomer.address,            // Dispatch Address
                Addr2: "...",                           // Additional Address
                Loc: fromCustomer.state,                // Dispatch Location
                Pin: fromCustomer.pincode,              // Dispatch PIN
                Stcd: fromCustomer.gstin.substring(0, 2) // Dispatch State Code
            },

            // Shipping Details (same as buyer for most cases)
            ShipDtls: {
                Gstin: toCustomer.gstin,                // Ship to GSTIN
                LglNm: toCustomer.name,                 // Ship to Legal Name
                TrdNm: toCustomer.name,                 // Ship to Trade Name
                Addr1: toCustomer.address,              // Ship to Address
                Addr2: "...",                           // Additional Address
                Loc: toCustomer.state,                  // Ship to Location
                Pin: toCustomer.pincode,                // Ship to PIN
                Stcd: toCustomer.gstin.substring(0, 2), // Ship to State Code
            },

            // Value Details (calculated tax amounts)
            ValDtls: {
                AssVal: invTaxVal.AssVal,               // Assessable Value
                CgstVal: invTaxVal.CgstVal,             // CGST Amount
                SgstVal: invTaxVal.SgstVal,             // SGST Amount
                IgstVal: invTaxVal.IgstVal,             // IGST Amount
                CesVal: invTaxVal.CesVal || 0,          // Cess Amount
                OthChrg: invTaxVal.OthChrg,             // Other Charges
                Discount: data.discount || 0,           // Total Discount
                TotInvVal: invTaxVal.TotInvVal,         // Total Invoice Value
                RndOffAmt: invTaxVal.RndOffAmt,         // Round Off Amount
                TotInvValFc: invTaxVal.TotInvValFc || 0, // Total Invoice Value in Foreign Currency
            },

            // Export Details (for export invoices - null for domestic)
            ExpDtls: {
                ShipBNo: null,                          // Shipping Bill Number
                ShipBDt: null,                          // Shipping Bill Date
                Port: null,                             // Port Code
            },

            // Item List (to be populated below)
            ItemList: []
        };

        // ========================================================================
        // STEP 5: PROCESS INVOICE ITEMS
        // ========================================================================

        console.log(chalk.blue(`${logPrefix} Processing ${data.items.length} invoice items...`));

        let serialNumber = 1;
        for (const item of data.items) {
            // Validate required item fields
            if (!item.totalCost || item.gstRate === undefined) {
                console.error(chalk.red(`${logPrefix} Item ${serialNumber}: Missing totalCost or gstRate`));
                return {
                    success: false,
                    error: 'Invalid item data',
                    details: `Item ${serialNumber}: totalCost and gstRate are required`
                };
            }

            // Calculate item-level tax values
            const itemTaxVal = taxUtils.itemBasedGSTCalc({
                unitPrice: item.rate,
                quantity: item.quantity || 1,
                discount: item.discount || 0,
                gstRate: item.gstRate,
                sellerGstin: fromCustomer.gstin,
                buyerGstin: toCustomer.gstin,
                roundOff: 0,
                otherCharges: 0,
                cessRate: item.cessRate || 0,
                forceGstType: item.forceGstType || "IGST"
            });

            // Build item object for GSP request
            const gspItem = {
                SlNo: serialNumber.toString(),              // Serial Number
                PrdDesc: item.skuName || "",                // Product Description
                IsServc: item.isService === "Y" ? "Y" : "N", // Is Service (Y/N)
                HsnCd: item.hsnCode || "",                  // HSN/SAC Code
                Qty: item.quantity || 1,                    // Quantity
                Unit: item.unit || "PCS",                   // Unit of Measurement
                UnitPrice: item.unitPrice || 0.00,         // Unit Price
                TotAmt: item.totalCost,                     // Total Amount (before tax)
                Discount: itemTaxVal.discount || 0.00,     // Discount Amount
                AssAmt: itemTaxVal.assVal || 0.00,         // Assessable Amount
                GstRt: item.gstRate,                        // GST Rate (%)
                IgstAmt: itemTaxVal.igst || 0.00,          // IGST Amount
                CgstAmt: itemTaxVal.cgst || 0.00,          // CGST Amount
                SgstAmt: itemTaxVal.sgst || 0.00,          // SGST Amount
                CesRt: item.cessRate || 0.00,              // Cess Rate (%)
                CesAmt: itemTaxVal.cess || 0.00,           // Cess Amount
                TotItemVal: itemTaxVal.TotInvVal || 0.00,  // Total Item Value (including tax)
            };

            gspRequestData.ItemList.push(gspItem);
            serialNumber++;
        }

        // ========================================================================
        // STEP 6: SEND REQUEST TO GSP SERVICE
        // ========================================================================

        console.log(chalk.blue(`${logPrefix} Sending request to GSP service...`));

        // Prepare request headers
        const headers = {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'user_name': gspConfig.gsp_user_name,
            'password': gspConfig.gsp_password,
            'requestid': requestId,
            'gstin': fromCustomer.gstin,
        };

        const response = await axiosUtils.globalSendPostRequest(
            headers,
            local_basedUrl,
            GSP_CONSTANTS.ENDPOINTS.INVOICE,
            gspRequestData,
            GSP_CONSTANTS.TIMEOUTS.REQUEST
        );

        // ========================================================================
        // STEP 7: VALIDATE AND PROCESS RESPONSE
        // ========================================================================

        if (!response?.data) {
            throw new Error('Invalid response from GSP service');
        }

        if (!response.data.result) {
            console.error(chalk.red(`${logPrefix} Missing result data in GSP response`));
            return {
                success: false,
                error: 'Invalid GSP response',
                details: 'Missing result data in GSP response'
            };
        }

        console.log(chalk.green(`${logPrefix} Invoice created successfully`));

        // Return structured response
        return {
            success: true,
            data: {
                controlCode: response.data.result.Irn,              // Invoice Reference Number (IRN)
                documentNumber: response.data.result.AckNo,         // Acknowledgment Number
                documentDate: response.data.result.AckDt,           // Acknowledgment Date
                qrCode: response.data.result.SignedQRCode,          // QR Code for the invoice
                status: response.data.result.Status,                // Invoice Status
                EwbNo: response.data.result.EwbNo || "",           // E-Way Bill Number (if generated)
                EwbDt: response.data.result.EwbDt || "",           // E-Way Bill Date
                EwbValidTill: response.data.result.EwbValidTill || "", // E-Way Bill Valid Till
                SignedInvoice: response.data.result.SignedInvoice || "", // Digitally Signed Invoice
                requestId                                           // Request ID for tracking
            }
        };

    } catch (error) {
        console.error(chalk.red(`${logPrefix} Unexpected error:`), error);
        return {
            success: false,
            error: 'Unexpected error during invoice creation',
            details: error.message,
            requestId
        };
    }
};

const cancelGSPInvoice = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': data.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/invoice/cancel',
            {
                "irn": data.invoiceNumber,
                "cnlrsn": data.cancelReason,
                "cnlrem": data.cancelRemark
            }
        );

        console.log(chalk.green('GSP Invoice Cancelled Successfully'));

        return {
            controlCode: response.data.result.Irn,
            cancelDate: response.data.result.CancelDate,
        };

    } catch (error) {
        console.error('GSP Invoice Cancellation Error:', error.message || error);
        return { error: error.message || error };
    }
};

const getGSPInvoice = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': data.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/invoice/irn',
            { "irn": data.invoiceNumber }
        );

        console.log(chalk.green('GSP Invoice Retrieved Successfully'));

        return {
            controlCode: response.data.result.Irn,
            documentNumber: response.data.result.AckNo,
            documentDate: response.data.result.AckDt,
            qrCode: response.data.result.SignedQRCode,
            status: response.data.result.Status,
            EwbNo: response.data.result.EwbNo || "",
            EwbDt: response.data.result.EwbDt || "",
            EwbValidTill: response.data.result.EwbValidTill || ""
        }

    } catch (error) {
        console.error('GSP Invoice Retrieval Error:', error.message || error);
        return { error: error.message || error };
    }
};

const getTaxPayerDetails = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': data.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/master/gstin',
            { "gstin": data.gstin }
        );

        console.log(chalk.green('GSP Taxpayer Details Retrieved Successfully'));

        if (!response.data || !response.data.result) {
            return { error: 'No Tax Payer Found ' };
        }
        return {
            gstin: response.data.result.Gstin,
            legalName: response.data.result.LegalName,
            name: response.data.result.TradeName,
            address: response.data.result.AddrBnm,
            state: response.data.result.AddrSt,
            location: response.data.result.AddrLoc,
            pincode: response.data.result.AddrPncd,
            stateCode: response.data.result.StateCode,
            taxPayType: response.data.result.TxpType,
            status: response.data.result.Status,
            blkStatus: response.data.result.BlkStatus,
        };

    } catch (error) {
        console.error('GSP Taxpayer Details Retrieval Error:', error.message || error);
        return { error: error.message || error };
    }
};


const createGSPEWayBill = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        let requestData = {
            Irn: data.controlCode,
            TransId: data.uid,
            TransMode: "1",
            TrnDocNO: data.voucherNumber,
            TrnDocDt: data.voucherTime,
            VehNo: data.vehicleNumber || "",
            Distance: data.distance || 0,
            VehType: data.vehicleType || "01",
            TransName: data.transportName || "",
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': data.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/ewaybill',
            requestData
        );

        console.log(chalk.green('GSP E-Way Bill Created Successfully'));

        return {
            billNumber: response.data.result.EwayBillNo,
            billDate: response.data.result.EwayBillDate,
            billValidUpto: response.data.result.ValidUpto,
        };

    } catch (error) {
        console.error('GSP E-Way Bill Creation Error:', error.message || error);
        return { error: error.message || error };
    }
};

const cancelGSPWayBill = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': data.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/ewaybill',
            {
                "ewbNo": data.billNumber,
                "cancelRmrk": data.cancelReason,
             }
        );

        console.log(chalk.green('GSP E-Way Bill Cancelled Successfully'));

        return {
            billNumber: response.data.result.ewayBillNo,
            cancelDate: response.data.result.cancelDate,
        };

    } catch (error) {
        console.error('GSP E-Way Bill Cancellation Error:', error.message || error);
        return { error: error.message || error };
    }
};

const getGSPWayBill = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': data.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/ewaybill/irn',
            { "irn": data.bcontrolCode }
        );

        console.log(chalk.green('GSP E-Way Bill Retrieved Successfully'));

        return {
            billNumber: response.data.result.EwayBillNo,
            billDate: response.data.result.EwayBillDate,
            billValidUpto: response.data.result.ValidUpto,
            status: response.data.result.Status,
            genGstin : response.data.result.GenGstin || "",
        };

    } catch (error) {
        console.error('GSP E-Way Bill Retrieval Error:', error.message || error);
        return { error: error.message || error };
    }
}

const getGSPInvoiceBasedOnDocumentNumber = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': data.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/invoice/document',
            { "docNo": data.documentNumber }
        );

        console.log(chalk.green('GSP Invoice Retrieved Successfully'));

        return {
            controlCode: response.data.result.Irn,
            documentNumber: response.data.result.AckNo,
            documentDate: response.data.result.AckDt,
            qrCode: response.data.result.SignedQRCode,
            status: response.data.result.Status,
        };

    } catch (error) {
        console.error('GSP Invoice Retrieval Error:', error.message || error);
        return { error: error.message || error };
    }
}
const createGSTCreditNotes = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const fromCustomer = data.fromFirm;
        const toCustomer = data.toFirm;

        if (!fromCustomer || !toCustomer) {
            console.error(chalk.red('From or To customer details are missing'));
            return { error: 'From or To customer details are missing' };
        }

        const invTaxVal = taxUtils.generateInvoiceValDtls({
            items: data.items,
            sellerGstin: fromCustomer.gstin,
            buyerGstin: toCustomer.gstin,
            otherCharges: data.otherCharges || 0,
            roundOff: data.roundOff || 0,
        });

        const gspRequestData = {
            Version: "1.1",
            TranDtls: {
                TaxSch: "GST",
                SupTyp: "B2B",
                RegRev: "N",
                EcmGstin: null,
                IgstOnIntra: data.igstOnIntra || "N",
            },
            DocDtls: {
                Typ: "CRN",
                No: data.voucherNumber,
                Dt: data.voucherTime,
            },
            SellerDtls: {
                Gstin: fromCustomer.gstin,
                LglNm: fromCustomer.name,
                TrdNm: fromCustomer.name,
                Addr1: fromCustomer.address,
                Addr2: "",
                Loc: fromCustomer.state,
                Pin: fromCustomer.pincode,
                Stcd: fromCustomer.gstin.substring(0, 2),
                Em: fromCustomer.email || ""
            },
            BuyerDtls: {
                Gstin: toCustomer.gstin,
                LglNm: toCustomer.name,
                Pos: toCustomer.gstin.substring(0, 2),
                Addr1: toCustomer.address,
                Addr2: "",
                Loc: toCustomer.state,
                Pin: toCustomer.pincode,
                Stcd: toCustomer.gstin.substring(0, 2),
                Em: toCustomer.email || ""
            },
            ValDtls: {
                AssVal: invTaxVal.AssVal,
                CgstVal: invTaxVal.CgstVal,
                SgstVal: invTaxVal.SgstVal,
                IgstVal: invTaxVal.IgstVal,
                TotInvVal: invTaxVal.TotInvVal,
            },
            PrevDocDtls: {
                InvNo: data.referenceVocherName,
                InvDt: data.referenceVocherTime
            },
            ItemList: []
        };

        if (Array.isArray(data.items) && data.items.length > 0) {
            let serialNumber = 1;
            for (let item of data.items) {
                if (!item.totalCost || !item.gstRate) {
                    return { error: 'Item totalCost or gstRate is missing' };
                }

                const itemTaxVal = taxUtils.itemBasedGSTCalc({
                    unitPrice: item.rate,
                    quantity: item.quantity || 1,
                    discount: item.discount || 0,
                    gstRate: item.gstRate,
                    sellerGstin: fromCustomer.gstin,
                    buyerGstin: toCustomer.gstin,
                    roundOff: 0,
                    otherCharges: 0,
                    cessRate: item.cessRate || 0,
                    forceGstType: item.forceGstType || "IGST"
                });

                gspRequestData.ItemList.push({
                    SlNo: serialNumber.toString(),
                    PrdDesc: item.skuName || "",
                    IsServc: item.isService === "Y" ? "Y" : "N",
                    HsnCd: item.hsnCode || "",
                    Qty: item.quantity || 1,
                    Unit: item.unit || "PCS",
                    UnitPrice: item.unitPrice || 0.00,
                    TotAmt: item.totalCost,
                    Discount: itemTaxVal.discount || 0.00,
                    AssAmt: itemTaxVal.assVal || 0.00,
                    GstRt: item.gstRate,
                    IgstAmt: itemTaxVal.igst || 0.00,
                    CgstAmt: itemTaxVal.cgst || 0.00,
                    SgstAmt: itemTaxVal.sgst || 0.00,
                    CesRt: item.cessRate || 0.00,
                    CesAmt: itemTaxVal.cess || 0.00,
                    TotItemVal: itemTaxVal.TotInvVal || 0.00,
                });
                serialNumber++;
            }
        } else {
            return { error: 'Invoice item list is empty or invalid' };
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': fromCustomer.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/invoice',
            gspRequestData
        );

        console.log(chalk.green('GSP Invoice Created Successfully'));
        return {
            controlCode: response.data.result.Irn,
            documentNumber: response.data.result.AckNo,
            documentDate: response.data.result.AckDt,
            qrCode: response.data.result.SignedQRCode,
            status: response.data.result.Status,
            EwbNo: response.data.result.EwbNo || "",
            EwbDt: response.data.result.EwbDt || "",
            EwbValidTill: response.data.result.EwbValidTill || "",
            SignedInvoice: response.data.result.SignedInvoice || "",
        };

    } catch (error) {
        console.error('GSP Create Notes Creation Error:', error.message || error);
        return { error: error.message || error };
    }
}

const createGSTDebitNotes = async (data) => {
    try {
        let accessToken = await redisGet('GSP_ACCESS_TOKEN');

        // If no token found, try login
        if (!accessToken) {
            console.warn(chalk.yellow('GSP Access Token not found in Redis. Trying to regenerate...'));
            const loginResponse = await gspLogin(data);

            if (!loginResponse || !loginResponse.access_token) {
                throw new Error('Unable to get access token from GSP');
            }

            accessToken = loginResponse.access_token;
        }

        const fromCustomer = data.fromFirm;
        const toCustomer = data.toFirm;

        if (!fromCustomer || !toCustomer) {
            console.error(chalk.red('From or To customer details are missing'));
            return { error: 'From or To customer details are missing' };
        }

        const invTaxVal = taxUtils.generateInvoiceValDtls({
            items: data.items,
            sellerGstin: fromCustomer.gstin,
            buyerGstin: toCustomer.gstin,
            otherCharges: data.otherCharges || 0,
            roundOff: data.roundOff || 0,
        });

        const gspRequestData = {
            Version: "1.1",
            TranDtls: {
                TaxSch: "GST",
                SupTyp: "B2B",
                RegRev: "N",
                EcmGstin: null,
                IgstOnIntra: data.igstOnIntra || "N",
            },
            DocDtls: {
                Typ: "DBN",
                No: data.voucherNumber,
                Dt: data.voucherTime,
            },
            SellerDtls: {
                Gstin: fromCustomer.gstin,
                LglNm: fromCustomer.name,
                TrdNm: fromCustomer.name,
                Addr1: fromCustomer.address,
                Addr2: "",
                Loc: fromCustomer.state,
                Pin: fromCustomer.pincode,
                Stcd: fromCustomer.gstin.substring(0, 2),
                Em: fromCustomer.email || ""
            },
            BuyerDtls: {
                Gstin: toCustomer.gstin,
                LglNm: toCustomer.name,
                Pos: toCustomer.gstin.substring(0, 2),
                Addr1: toCustomer.address,
                Addr2: "",
                Loc: toCustomer.state,
                Pin: toCustomer.pincode,
                Stcd: toCustomer.gstin.substring(0, 2),
                Em: toCustomer.email || ""
            },
            ValDtls: {
                AssVal: invTaxVal.AssVal,
                CgstVal: invTaxVal.CgstVal,
                SgstVal: invTaxVal.SgstVal,
                IgstVal: invTaxVal.IgstVal,
                TotInvVal: invTaxVal.TotInvVal
            },
            PrevDocDtls: {
                InvNo: data.referenceVocherName,
                InvDt: data.referenceVocherTime
            },
            ItemList: []
        };

        if (Array.isArray(data.items) && data.items.length > 0) {
            let serialNumber = 1;
            for (let item of data.items) {
                if (!item.totalCost || !item.gstRate) {
                    return { error: 'Item totalCost or gstRate is missing' };
                }

                const itemTaxVal = taxUtils.itemBasedGSTCalc({
                    unitPrice: item.rate,
                    quantity: item.quantity || 1,
                    discount: item.discount || 0,
                    gstRate: item.gstRate,
                    sellerGstin: fromCustomer.gstin,
                    buyerGstin: toCustomer.gstin,
                    roundOff: 0,
                    otherCharges: 0,
                    cessRate: item.cessRate || 0,
                    forceGstType: item.forceGstType || "IGST"
                });

                gspRequestData.ItemList.push({
                    SlNo: serialNumber.toString(),
                    PrdDesc: item.skuName || "",
                    IsServc: item.isService === "Y" ? "Y" : "N",
                    HsnCd: item.hsnCode || "",
                    Qty: item.quantity || 1,
                    Unit: item.unit || "PCS",
                    UnitPrice: item.unitPrice || 0.00,
                    TotAmt: item.totalCost,
                    Discount: itemTaxVal.discount || 0.00,
                    AssAmt: itemTaxVal.assVal || 0.00,
                    GstRt: item.gstRate,
                    IgstAmt: itemTaxVal.igst || 0.00,
                    CgstAmt: itemTaxVal.cgst || 0.00,
                    SgstAmt: itemTaxVal.sgst || 0.00,
                    CesRt: item.cessRate || 0.00,
                    CesAmt: itemTaxVal.cess || 0.00,
                    TotItemVal: itemTaxVal.TotInvVal || 0.00,
                });
                serialNumber++;
            }
        } else {
            return { error: 'Item list is empty or invalid' };
        }

        const response = await axiosUtils.globalSendPostRequest(
            {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'user_name': gspConfig.gsp_user_name,
                'password': gspConfig.gsp_password,
                'requestid': data.uid,
                'gstin': fromCustomer.gstin,
            },
            local_basedUrl,
            '/enriched/ei/api/invoice',
            gspRequestData
        );

        console.log(chalk.green('GSP Debit Notes Created Successfully'));

        return {
            controlCode: response.data.result.Irn,
            documentNumber: response.data.result.AckNo,
            documentDate: response.data.result.AckDt,
            qrCode: response.data.result.SignedQRCode,
            status: response.data.result.Status,
            EwbNo: response.data.result.EwbNo || "",
            EwbDt: response.data.result.EwbDt || "",
            EwbValidTill: response.data.result.EwbValidTill || "",
            SignedInvoice: response.data.result.SignedInvoice || "",
        };

    } catch (error) {
        console.error('GSP Debit Notes Creation Error:', error.message || error);
        return { error: error.message || error };
    }
}

// ============================================================================
// MODULE EXPORTS
// ============================================================================

module.exports = {
    // Authentication functions
    gspLogin,
    getValidAccessToken,

    // Invoice management functions
    createGSPInvoice,
    cancelGSPInvoice,
    getGSPInvoice,
    getGSPInvoiceBasedOnDocumentNumber,

    // Credit/Debit notes functions
    createGSTCreditNotes,
    createGSTDebitNotes,

    // E-Way Bill functions
    createGSPEWayBill,
    cancelGSPWayBill,
    getGSPWayBill,

    // Utility functions
    getTaxPayerDetails,

    // Constants for external use
    GSP_CONSTANTS
};