
// ============================================================================
// E-Way Bill API Routes
// ============================================================================
// This file contains all API routes for E-Way Bill operations including:
// - Create E-Way Bill (POST /create)
// - Cancel E-Way Bill (POST /cancel)
// - Get E-Way Bill (POST /get)
// ============================================================================

const express = require('express');
const router = express.Router();
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');

// Import controllers
const { createEWaysBillClt } = require('../controllers/createEWaysBillClt');
const { cancelEWaysBillClt } = require('../controllers/cancelEWaysBillClt');
const { getEWaysBillClt } = require('../controllers/getEWaysBillClt');

// Import middleware
const { validateToken, authenticateJWT } = require('../utils/authUtils');
const {
    apiRateLimit,
    validateInput,
    sanitizeInput
} = require('../middleware/securityMiddleware');

// Import validation schemas
const Joi = require('joi');

// ============================================================================

// Middleware stack for E-Way Bill routes
const ewayMiddleware = [
    apiRateLimit,           // Rate limiting
    sanitizeInput,          // Input sanitization
    validateToken,          // Legacy token validation (backward compatibility)
    // authenticateJWT,     // JWT authentication (uncomment to enable)
];

// ============================================================================

// 1. CREATE E-WAY BILL VALIDATION SCHEMA
const createEWaysBillSchema = Joi.object({
    ewaysBillData: Joi.object({
        controlCode: Joi.string().required().length(64).messages({
            'string.length': 'Control code (IRN) must be exactly 64 characters',
            'string.empty': 'Control code (IRN) is required for E-Way Bill generation'
        }),
        voucherNumber: Joi.string().required().min(1).max(50).messages({
            'string.empty': 'Voucher number is required',
            'string.max': 'Voucher number cannot exceed 50 characters'
        }),
        voucherTime: Joi.string().required().messages({
            'string.empty': 'Voucher time is required'
        }),

        vehicleNumber: Joi.string().required().pattern(/^[A-Z]{2}[0-9]{1,2}[A-Z]{1,2}[0-9]{4}$/).messages({
            'string.pattern.base': 'Vehicle number must be in valid Indian format (e.g., KA01AB1234)',
            'string.empty': 'Vehicle number is required'
        }),

        distance: Joi.number().min(1).max(4000).required().messages({
            'number.min': 'Distance must be at least 1 km',
            'number.max': 'Distance cannot exceed 4000 km',
            'any.required': 'Distance is required for E-Way Bill'
        }),

        vehicleType: Joi.string().required().valid('REGULAR', 'ODC').messages({
            'any.only': 'Vehicle type must be: REGULAR or ODC',
            'string.empty': 'Vehicle type is required'
        }), transportName: Joi.string().required().min(1).max(100).messages({
            'string.empty': 'Transport name is required',
            'string.max': 'Transport name cannot exceed 100 characters'
        }),

        transporterId: Joi.string().optional().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'Transporter ID must be a valid GSTIN format'
        }), transportMode: Joi.string().optional().valid('1', '2', '3', '4').default('1').messages({
            'any.only': 'Transport mode must be: 1=Road, 2=Rail, 3=Air, 4=Ship'
        }), docType: Joi.string().optional().valid('INV', 'BIL', 'BOE', 'CHL', 'CNT', 'OTH').default('INV').messages({
            'any.only': 'Document type must be: INV=Invoice, BIL=Bill of Supply, BOE=Bill of Entry, CHL=Challan, CNT=Credit Note, OTH=Others'
        }),

        uid: Joi.string().uuid().optional()
    }).required()
});

// 2. CANCEL E-WAY BILL VALIDATION SCHEMA
const cancelEWaysBillSchema = Joi.object({
    cancelData: Joi.object({
        ewayBillNumber: Joi.string().required().length(12).pattern(/^[0-9]{12}$/).messages({
            'string.length': 'E-Way Bill number must be exactly 12 digits',
            'string.pattern.base': 'E-Way Bill number must contain only digits',
            'string.empty': 'E-Way Bill number is required for cancellation'
        }),
        cancelReason: Joi.string().required().valid('1', '2', '3', '4').messages({
            'any.only': 'Cancel reason must be: 1=Duplicate, 2=Data Entry Error, 3=Order Cancelled, 4=Others',
            'string.empty': 'Cancel reason is required'
        }),
        cancelRemark: Joi.string().required().min(1).max(100).messages({
            'string.empty': 'Cancel remark is required',
            'string.max': 'Cancel remark cannot exceed 100 characters',
            'string.min': 'Cancel remark must be at least 1 character'
        }),
        gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)',
            'string.empty': 'GSTIN is required for E-Way Bill cancellation'
        }),
        uid: Joi.string().uuid().optional()
    }).required()
});

// 3. GET E-WAY BILL VALIDATION SCHEMA      
const getEWaysBillSchema = Joi.object({
    ewaysBillData: Joi.object({
        irn: Joi.string().required().length(64).messages({
            'string.length': 'IRN must be exactly 64 characters',
            'string.empty': 'IRN is required for E-Way Bill retrieval'
        }),
        gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)',
            'string.empty': 'GSTIN is required for E-Way Bill retrieval'
        }),
        uid: Joi.string().uuid().optional()
    }).required()
});

// ============================================================================
// E-WAY BILL API ROUTES
// ============================================================================

/**
 * @route POST /api/eway-bill/create
 * @desc Create a new E-Way Bill
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication, validation
 * @body {Object} ewaysBillData - Complete E-Way Bill data
 * @example
 * POST /api/eway-bill/create
 * {
 *   "ewaysBillData": {
 *     "controlCode": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
 *     "voucherNumber": "INV-001",
 *     "voucherTime": "2024-01-15",
 *     "vehicleNumber": "KA01AB1234",
 *     "distance": 150,
 *     "vehicleType": "REGULAR",
 *     "transportName": "ABC Transport Services",
 *     "transporterId": "29ABCDE1234F1Z5",
 *     "transportMode": "1",
 *     "docType": "INV"
 *   }
 * }
 */
router.post('/create',
    ...ewayMiddleware,
    validateInput(createEWaysBillSchema),
    createEWaysBillClt
);

/**
 * @route POST /api/eway-bill/cancel
 * @desc Cancel an existing E-Way Bill
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication, validation
 * @body {Object} cancelData - E-Way Bill number, cancel reason, remark, and GSTIN
 * @example
 * POST /api/eway-bill/cancel
 * {
 *   "cancelData": {
 *     "ewayBillNumber": "123456789012",
 *     "cancelReason": "1",
 *     "cancelRemark": "Duplicate E-Way Bill created by mistake",
 *     "gstin": "29ABCDE1234F1Z5"
 *   }
 * }
 */

router.post('/cancel',
    ...ewayMiddleware,
    validateInput(cancelEWaysBillSchema),
    cancelEWaysBillClt
);

/**
 * @route POST /api/eway-bill/get
 * @desc Get E-Way Bill details by IRN (Invoice Reference Number)
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication, validation
 * @body {Object} ewaysBillData - IRN and GSTIN
 * @example
 * POST /api/eway-bill/get
 * {
 *   "ewaysBillData": {
 *     "irn": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
 *     "gstin": "29ABCDE1234F1Z5"
 *   }
 * }
 */
router.post('/get',
    ...ewayMiddleware,
    validateInput(getEWaysBillSchema),
    getEWaysBillClt
);

module.exports = router;

















