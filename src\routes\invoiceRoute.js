// ============================================================================
// GSP Invoice API Routes
// ============================================================================
// This file contains all API routes for GSP invoice operations including:
// - Create Invoice (POST /create)
// - Cancel Invoice (POST /cancel)
// - Get Invoice by IRN (GET /get/:controlCode)
// - Get Invoice by Document Number (GET /document/:documentNumber)
// ============================================================================

const express = require('express');
const router = express.Router();
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');

// Import controllers
const { createInvoiceClt } = require('../controllers/createInvoiceClt');
const { cancelInvoiceClt } = require('../controllers/cancelInvoiceClt');
const { getInvoiceByIrnClt } = require('../controllers/getInvoiceClt');
const { getInvoiceByDocumentClt } = require('../controllers/getInvoiceClt');

// Import middleware
const { validateToken, authenticateJWT } = require('../utils/authUtils');
const {
    apiRateLimit,
    validateInput,
    sanitizeInput
} = require('../middleware/securityMiddleware');

// Import validation schemas
const Joi = require('joi');

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

// Common GSTIN validation pattern
const gstinPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

// Common firm details schema
const firmSchema = Joi.object({
    gstin: Joi.string().required().pattern(gstinPattern).messages({
        'string.pattern.base': 'GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)'
    }),
    email: Joi.string().email().optional()
});

// Invoice item schema
const itemSchema = Joi.object({
    
    hsnCode: Joi.string().required().pattern(/^[0-9]{4,8}$/).messages({
        'string.pattern.base': 'HSN Code must be 4-8 digits'
    }),
    quantity: Joi.number().positive().required(),
    rate: Joi.number().positive().required(),
    totalCost: Joi.number().positive().required(),
    gstRate: Joi.number().min(0).max(28).required(),
    isService: Joi.string().valid('Y', 'N').optional().default('N'),
});

// 1. CREATE INVOICE VALIDATION SCHEMA
const createInvoiceSchema = Joi.object({
    invoiceData: Joi.object({
        voucherNumber: Joi.string().required().min(1).max(50),
        voucherTime: Joi.string().required(),
        fromFirm: firmSchema.required(),
        toFirm: firmSchema.required(),
        items: Joi.array().items(itemSchema).min(1).required(),
        uid: Joi.string().uuid().optional(),
        igstOnIntra: Joi.string().valid('Y', 'N').optional().default('N'),
        discount: Joi.number().min(0).optional().default(0),
    }).required()
});

// 2. CANCEL INVOICE VALIDATION SCHEMA
const cancelInvoiceSchema = Joi.object({
    controlCode: Joi.string().required().length(64).messages({
        'string.length': 'Control code must be exactly 64 characters'
    }),
    cancelReason: Joi.string().required().valid('1', '2', '3', '4').messages({
        'any.only': 'Cancel reason must be: 1=Duplicate, 2=Data Entry Error, 3=Order Cancelled, 4=Others'
    }),
    cancelRemark: Joi.string().required().min(1).max(100),
    gstin: Joi.string().required().pattern(gstinPattern),
    uid: Joi.string().uuid().optional()
});

// 3. GET INVOICE BY IRN VALIDATION SCHEMA
const getInvoiceByIrnSchema = Joi.object({
    controlCode: Joi.string().required().length(64).messages({
        'string.length': 'Control code must be exactly 64 characters'
    }),
    gstin: Joi.string().required().pattern(gstinPattern),
    uid: Joi.string().uuid().optional()
});

// 4. GET INVOICE BY DOCUMENT NUMBER VALIDATION SCHEMA
const getInvoiceByDocumentSchema = Joi.object({
    documentNumber: Joi.string().required().min(1).max(50),
    gstin: Joi.string().required().pattern(gstinPattern),
    uid: Joi.string().uuid().optional()
});

// Parameter validation schemas
const irnParamSchema = Joi.object({
    controlCode: Joi.string().required().length(64).messages({
        'string.length': 'Control code parameter must be exactly 64 characters'
    })
});

const documentNumberParamSchema = Joi.object({
    documentNumber: Joi.string().required().min(1).max(50)
});

// Middleware stack for invoice routes
const invoiceMiddleware = [
    apiRateLimit,           // Rate limiting
    sanitizeInput,          // Input sanitization
    validateToken,          // Legacy token validation (backward compatibility)
    // authenticateJWT,     // JWT authentication (uncomment to enable)
];

// Parameter validation middleware
const validateParams = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.params);
        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            return res.status(400).json({
                success: false,
                error: 'Parameter validation failed',
                details: validationErrors,
                timestamp: new Date().toISOString()
            });
        }
        next();
    };
};

// ============================================================================
// API ROUTES
// ============================================================================

/**
 * @route POST /api/invoice/create
 * @desc Create a new GSP invoice
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication, validation
 * @body {Object} invoiceData - Complete invoice data with firm details and items
 * @example
 * POST /api/invoice/create
 * {
 *   "invoiceData": {
 *     "voucherNumber": "INV-001",
 *     "voucherTime": "2024-01-15",
 *     "fromFirm": { "gstin": "29ABCDE1234F1Z5", "name": "Seller Company", ... },
 *     "toFirm": { "gstin": "27FGHIJ5678K2L9", "name": "Buyer Company", ... },
 *     "items": [{ "skuName": "Product 1", "hsnCode": "1234", "quantity": 1, ... }]
 *   }
 * }
 */
router.post('/create',
    ...invoiceMiddleware,
    validateInput(createInvoiceSchema),
    createInvoiceClt
);

/**
 * @route POST /api/invoice/cancel
 * @desc Cancel an existing GSP invoice
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication, validation
 * @body {Object} cancelData - IRN, cancel reason, remark, and GSTIN
 * @example
 * POST /api/invoice/cancel
 * {
 *   "controlCode": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
 *   "cancelReason": "1",
 *   "cancelRemark": "Duplicate invoice created by mistake",
 *   "gstin": "29ABCDE1234F1Z5"
 * }
 */
router.post('/cancel',
    ...invoiceMiddleware,
    validateInput(cancelInvoiceSchema),
    cancelInvoiceClt
);

/**
 * @route POST /api/invoice/get/:controlCode
 * @desc Get GSP invoice details by IRN (Invoice Reference Number)
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication, validation
 * @param {string} controlCode - 64-character Invoice Reference Number
 * @body {Object} requestData - GSTIN and optional UID
 * @example
 * POST /api/invoice/get/1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
 * {
 *   "gstin": "29ABCDE1234F1Z5"
 * }
 */
router.post('/get/:controlCode',
    ...invoiceMiddleware,
    validateParams(irnParamSchema),
    validateInput(getInvoiceByIrnSchema),
    getInvoiceByIrnClt
);

/**
 * @route POST /api/invoice/document/:documentNumber
 * @desc Get GSP invoice details by document number
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication, validation
 * @param {string} documentNumber - Invoice document number
 * @body {Object} requestData - GSTIN and optional UID
 * @example
 * POST /api/invoice/document/INV-001
 * {
 *   "gstin": "29ABCDE1234F1Z5"
 * }
 */
router.post('/document/:documentNumber',
    ...invoiceMiddleware,
    validateParams(documentNumberParamSchema),
    validateInput(getInvoiceByDocumentSchema),
    getInvoiceByDocumentClt
);

/**
 * @route GET /api/invoice/health
 * @desc Health check for invoice service
 * @access Public
 */
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'gsp-invoice-service',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        endpoints: {
            create: 'POST /api/invoice/create',
            cancel: 'POST /api/invoice/cancel',
            getByIrn: 'POST /api/invoice/get/:controlCode',
            getByDocument: 'POST /api/invoice/document/:documentNumber'
        }
    });
});

// Error handling middleware for this router
router.use((err, req, res, next) => {
    console.error(chalk.red('[Invoice Routes] Error:'), err);

    res.status(err.status || 500).json({
        error: 'Invoice service error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Internal service error',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;