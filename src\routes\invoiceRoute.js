const express = require('express');
const router = express.Router();
const chalk = require('chalk');

// Import controllers
const { createInvoiceClt } = require('../controllers/createInvoiceClt');

// Import middleware
const { validateToken, authenticateJWT } = require('../utils/authUtils');
const {
    apiRateLimit,
    validateInput,
    sanitizeInput
} = require('../middleware/securityMiddleware');

// Import validation schemas
const Joi = require('joi');

// Validation schemas
const createInvoiceSchema = Joi.object({
    invoiceData: Joi.object({
        voucherNumber: Joi.string().required().min(1).max(50),
        voucherTime: Joi.string().required(),
        fromFirm: Joi.object({
            gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/),
            name: Joi.string().required().min(1).max(200),
            address: Joi.string().required().min(1).max(500),
            state: Joi.string().required().min(1).max(100),
            pincode: Joi.string().required().pattern(/^[0-9]{6}$/),
            email: Joi.string().email().optional()
        }).required(),
        toFirm: Joi.object({
            gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/),
            name: Joi.string().required().min(1).max(200),
            address: Joi.string().required().min(1).max(500),
            state: Joi.string().required().min(1).max(100),
            pincode: Joi.string().required().pattern(/^[0-9]{6}$/),
            email: Joi.string().email().optional()
        }).required(),
        items: Joi.array().items(
            Joi.object({
                skuName: Joi.string().required().min(1).max(200),
                hsnCode: Joi.string().required().pattern(/^[0-9]{4,8}$/),
                quantity: Joi.number().positive().required(),
                rate: Joi.number().positive().required(),
                totalCost: Joi.number().positive().required(),
                gstRate: Joi.number().min(0).max(28).required(),
                unit: Joi.string().optional().default('PCS'),
                isService: Joi.string().valid('Y', 'N').optional().default('N'),
                cessRate: Joi.number().min(0).optional().default(0),
                discount: Joi.number().min(0).optional().default(0)
            })
        ).min(1).required(),
        uid: Joi.string().uuid().optional(),
        igstOnIntra: Joi.string().valid('Y', 'N').optional().default('N'),
        discount: Joi.number().min(0).optional().default(0),
        otherCharges: Joi.number().min(0).optional().default(0),
        roundOff: Joi.number().optional().default(0)
    }).required()
});

// Middleware stack for invoice routes
const invoiceMiddleware = [
    apiRateLimit,           // Rate limiting
    sanitizeInput,          // Input sanitization
    validateToken,          // Legacy token validation (backward compatibility)
    // authenticateJWT,     // JWT authentication (uncomment to enable)
];

// Routes
/**
 * @route POST /api/invoice/create
 * @desc Create a new invoice
 * @access Private
 * @middleware Rate limiting, input sanitization, authentication
 */
router.post('/create',
    ...invoiceMiddleware,
    validateInput(createInvoiceSchema),
    createInvoiceClt
);

/**
 * @route GET /api/invoice/health
 * @desc Health check for invoice service
 * @access Public
 */
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'invoice-service',
        timestamp: new Date().toISOString(),
        version: '2.0.0'
    });
});

// Error handling middleware for this router
router.use((err, req, res, next) => {
    console.error(chalk.red('[Invoice Routes] Error:'), err);

    res.status(err.status || 500).json({
        error: 'Invoice service error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Internal service error',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;