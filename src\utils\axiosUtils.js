
const axios = require('axios');
const chalk = require('chalk');

/**
 * Utility to construct full API URL safely (avoiding double slashes).
 */
function buildFullUrl(base, path) {
    return `${base.replace(/\/$/, '')}/${path.replace(/^\//, '')}`;
}

/**
 * Global POST Request
 * @param {Object} headers - Headers to include in the request
 * @param {string} endpoint - Base URL for the API
 * @param {string} url - Specific endpoint to hit
 * @param {Object} [data={}] - Data to send in the POST request
 * @return {Promise<Object>} - Response data from the API
 */
async function globalSendPostRequest(headers, endpoint, url, data = {}) {
    try {
        const fullUrl = buildFullUrl(endpoint, url);
        const response = await axios.post(fullUrl, data, { headers });
        console.log(`✅ POST Success: ${fullUrl}`);
        return response.data;
    } catch (error) {
        console.error(chalk.red(`POST Error: ${url}`), error.response?.data || error.message);
        throw error;
    }
}

/**
 * Global GET Request
 * @param {Object} headers - Headers to include in the request
 * @param {string} endpoint - Base URL for the API
 * @param {string} url - Specific endpoint to hit
 * @param {Object} [params={}] - Query parameters to include in the GET request
 * @return {Promise<Object>} - Response data from the API
 */
async function globalSendGetRequest(headers, endpoint, url, params = {}) {
    try {
        const fullUrl = buildFullUrl(endpoint, url);
        const response = await axios.get(fullUrl, {
            headers,
            params
        });
        return response.data;
    } catch (error) {
        console.error(chalk.red(`GET Error: ${url}`), error.response?.data || error.message);
        throw error;
    }
}

/**
 * Global PUT Request
 * @param {Object} headers - Headers to include in the request
 * @param {string} endpoint - Base URL for the API
 * @param {string} url - Specific endpoint to hit
 * @param {Object} [data={}] - Data to send in the PUT request
 * @return {Promise<Object>} - Response data from the API
 */
async function globalSendPutRequest(headers, endpoint, url, data = {}) {
    try {
        const fullUrl = buildFullUrl(endpoint, url);
        const response = await axios.put(fullUrl, data, { headers });
        return response.data;
    } catch (error) {
        console.error(chalk.red(`PUT Error: ${url}`), error.response?.data || error.message);
        throw error;
    }
}


module.exports = {
    globalSendPostRequest,
    globalSendGetRequest,
    globalSendPutRequest,
};
