// ============================================================================
// Get Invoice Controller with Country-wise Processing
// ============================================================================
// This controller handles invoice retrieval with:
// - Country-specific processing logic (similar to createInvoiceClt)
// - Company validation and lookup
// - Comprehensive input validation
// - Enhanced error handling and logging
// - Request tracking and monitoring
// - Support for both IRN and Document Number based retrieval
// ============================================================================

const gspController = require('./sub-controllers/gspController');
const database = require('../model/DBClient');
const logs = require('../utils/utils');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const { ProcessMonitor } = require('../utils/processMonitor');

// Get process monitor instance
const processMonitor = ProcessMonitor ? new ProcessMonitor() : null;

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

// Get invoice by IRN validation schema
const getInvoiceByIrnValidationSchema = Joi.object({
    invoiceData: Joi.object({
        // Invoice Reference Number (IRN) - required for retrieval
        irn: Joi.string().required().length(64).messages({
            'string.length': 'IRN must be exactly 64 characters',
            'string.empty': 'IRN is required for invoice retrieval'
        }),

        // GSTIN of the company requesting the invoice
        gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)',
            'string.empty': 'GSTIN is required for invoice retrieval'
        }),

        // Optional tracking ID
        uid: Joi.string().uuid().optional()
    }).required()
});

// Get invoice by document number validation schema
const getInvoiceByDocumentValidationSchema = Joi.object({
    invoiceData: Joi.object({
        // Document/Voucher number
        documentNumber: Joi.string().required().min(1).max(50).messages({
            'string.empty': 'Document number is required for invoice retrieval',
            'string.max': 'Document number cannot exceed 50 characters'
        }),

        // GSTIN of the company requesting the invoice
        gstin: Joi.string().required().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'GSTIN must be in valid format (e.g., 29ABCDE1234F1Z5)',
            'string.empty': 'GSTIN is required for invoice retrieval'
        }),

        // Optional tracking ID
        uid: Joi.string().uuid().optional()
    }).required()
});

// ============================================================================
// MAIN CONTROLLER FUNCTIONS
// ============================================================================

/**
 * Get Invoice by IRN Controller
 */
const getInvoiceByIrnClt = async (req, res) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || uuidv4();

    try {
        // ========================================================================
        // STEP 1: REQUEST LOGGING AND VALIDATION
        // ========================================================================

        console.log(chalk.blue(`[Get Invoice IRN] Request ${requestId} started - IP: ${req.ip}`));

        // Validate input data
        const { error, value } = getInvoiceByIrnValidationSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            console.warn(chalk.yellow(`[Get Invoice IRN] Validation failed for request ${requestId}:`, validationErrors));

            // Log validation failure
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'get_invoice_by_irn',
                JSON.stringify(req.body),
                JSON.stringify({ error: 'Validation failed', details: validationErrors }),
                'VALIDATION_ERROR',
                0
            );

            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: validationErrors,
                requestId
            });
        }

        const { invoiceData } = value;

        // Add request ID to invoice data for tracking
        invoiceData.uid = invoiceData.uid || requestId;

        console.log(chalk.blue(`[Get Invoice IRN] Retrieving invoice with IRN: ${invoiceData.irn.substring(0, 20)}...`));

        return await processInvoiceRetrieval(req, res, invoiceData, requestId, startTime, 'IRN');

    } catch (error) {
        return await handleUnexpectedError(req, res, error, requestId, 'get_invoice_by_irn');
    }
};

/**
 * Get Invoice by Document Number Controller
 */
const getInvoiceByDocumentClt = async (req, res) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || uuidv4();

    try {
        // ========================================================================
        // STEP 1: REQUEST LOGGING AND VALIDATION
        // ========================================================================

        console.log(chalk.blue(`[Get Invoice Doc] Request ${requestId} started - IP: ${req.ip}`));

        // Validate input data
        const { error, value } = getInvoiceByDocumentValidationSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            console.warn(chalk.yellow(`[Get Invoice Doc] Validation failed for request ${requestId}:`, validationErrors));

            // Log validation failure
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'get_invoice_by_document',
                JSON.stringify(req.body),
                JSON.stringify({ error: 'Validation failed', details: validationErrors }),
                'VALIDATION_ERROR',
                0
            );

            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: validationErrors,
                requestId
            });
        }

        const { invoiceData } = value;

        // Add request ID to invoice data for tracking
        invoiceData.uid = invoiceData.uid || requestId;

        console.log(chalk.blue(`[Get Invoice Doc] Retrieving invoice with document number: ${invoiceData.documentNumber}`));

        return await processInvoiceRetrieval(req, res, invoiceData, requestId, startTime, 'DOCUMENT');

    } catch (error) {
        return await handleUnexpectedError(req, res, error, requestId, 'get_invoice_by_document');
    }
};

// ============================================================================
// SHARED PROCESSING FUNCTIONS
// ============================================================================

/**
 * Process invoice retrieval with country-wise logic
 */
const processInvoiceRetrieval = async (req, res, invoiceData, requestId, startTime, retrievalType) => {
    try {
        // ========================================================================
        // STEP 2: COMPANY VALIDATION AND LOOKUP
        // ========================================================================

        // Get company from authenticated request (should be set by auth middleware)
        let company = req.company;

        // Fallback to header-based lookup (for backward compatibility)
        if (!company) {
            const companyUid = req.headers['company-uid'];

            if (!companyUid) {
                console.warn(chalk.yellow(`[Get Invoice ${retrievalType}] No company UID provided for request ${requestId}`));
                return res.status(400).json({
                    success: false,
                    error: 'Company UID is required',
                    requestId
                });
            }

            company = await database.CompanyInfo.findOne({
                attributes: ['companyUid', 'country_name', 'country_code'],
                raw: true,
                where: { companyUid: companyUid }
            });
        }

        if (!company) {
            console.warn(chalk.yellow(`[Get Invoice ${retrievalType}] Company not found for request ${requestId}`));

            // Log company not found
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                `get_invoice_by_${retrievalType.toLowerCase()}`,
                JSON.stringify({ companyUid: req.headers['company-uid'] }),
                JSON.stringify({ error: 'Company not found' }),
                'COMPANY_NOT_FOUND',
                0
            );

            return res.status(404).json({
                success: false,
                error: 'Company not found',
                requestId
            });
        }

        console.log(chalk.green(`[Get Invoice ${retrievalType}] Processing retrieval for company ${company.companyUid} (${company.country_code}) - Request ${requestId}`));

        // ========================================================================
        // STEP 3: COUNTRY-WISE PROCESSING
        // ========================================================================

        let response;
        let apiStatus = 1; // Success by default

        switch (company.country_code) {
            case 'IND':
                try {
                    console.log(chalk.blue(`[Get Invoice ${retrievalType}] Processing Indian invoice retrieval for request ${requestId}`));

                    // Map the data to match GSP controller expectations
                    let gspData;
                    let gspResponse;

                    if (retrievalType === 'IRN') {
                        gspData = {
                            ...invoiceData,
                            invoiceNumber: invoiceData.irn  // GSP controller expects invoiceNumber
                        };
                        gspResponse = await gspController.getGSPInvoice(gspData);
                    } else {
                        gspResponse = await gspController.getGSPInvoiceBasedOnDocumentNumber(invoiceData);
                    }

                    if (gspResponse.success === false || gspResponse.error) {
                        console.error(chalk.red(`[Get Invoice ${retrievalType}] GSP retrieval failed for request ${requestId}:`, gspResponse.error));
                        apiStatus = 0;

                        // Log GSP error
                        await logs.createApiLogs(
                            database,
                            req.user?.uid || 'anonymous',
                            `get_gsp_invoice_by_${retrievalType.toLowerCase()}`,
                            JSON.stringify(invoiceData),
                            JSON.stringify(gspResponse),
                            'GSP_ERROR',
                            0
                        );

                        return res.status(500).json({
                            success: false,
                            error: 'Failed to retrieve GSP invoice',
                            details: process.env.NODE_ENV === 'development' ? gspResponse.error : 'Internal processing error',
                            requestId
                        });
                    }

                    response = {
                        success: true,
                        message: `GSP Invoice retrieved successfully by ${retrievalType}`,
                        data: gspResponse.data || gspResponse,
                        requestId,
                        timestamp: new Date().toISOString()
                    };

                    console.log(chalk.green(`[Get Invoice ${retrievalType}] GSP invoice retrieved successfully for request ${requestId}`));

                } catch (gspError) {
                    console.error(chalk.red(`[Get Invoice ${retrievalType}] GSP processing error for request ${requestId}:`, gspError));
                    apiStatus = 0;

                    // Log GSP processing error
                    await logs.createApiLogs(
                        database,
                        req.user?.uid || 'anonymous',
                        `get_gsp_invoice_by_${retrievalType.toLowerCase()}`,
                        JSON.stringify(invoiceData),
                        JSON.stringify({ error: gspError.message }),
                        'GSP_PROCESSING_ERROR',
                        0
                    );

                    return res.status(500).json({
                        success: false,
                        error: 'GSP processing failed',
                        details: process.env.NODE_ENV === 'development' ? gspError.message : 'Internal processing error',
                        requestId
                    });
                }
                break;

            case 'USA':
                console.warn(chalk.yellow(`[Get Invoice ${retrievalType}] USA invoice retrieval not implemented for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    `get_invoice_by_${retrievalType.toLowerCase()}`,
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'Invoice retrieval not implemented for USA' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'Invoice retrieval not implemented for USA',
                    details: 'USA invoice retrieval system integration pending',
                    supportedCountries: ['IND'],
                    requestId
                });

            case 'CAN':
                console.warn(chalk.yellow(`[Get Invoice ${retrievalType}] Canada invoice retrieval not implemented for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    `get_invoice_by_${retrievalType.toLowerCase()}`,
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'Invoice retrieval not implemented for Canada' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'Invoice retrieval not implemented for Canada',
                    details: 'Canada invoice retrieval system integration pending',
                    supportedCountries: ['IND'],
                    requestId
                });

            default:
                console.warn(chalk.yellow(`[Get Invoice ${retrievalType}] Unsupported country code ${company.country_code} for request ${requestId}`));
                apiStatus = 0;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    `get_invoice_by_${retrievalType.toLowerCase()}`,
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'Unsupported country code' }),
                    'UNSUPPORTED_COUNTRY',
                    0
                );

                return res.status(400).json({
                    success: false,
                    error: 'Unsupported country code for invoice retrieval',
                    details: 'Invoice retrieval is currently only supported for Indian companies',
                    supportedCountries: ['IND'],
                    currentCountry: company.country_code,
                    requestId
                });
        }

        // ========================================================================
        // STEP 4: SUCCESS LOGGING AND RESPONSE
        // ========================================================================

        // Log successful operation
        await logs.createApiLogs(
            database,
            req.user?.uid || 'anonymous',
            `get_invoice_by_${retrievalType.toLowerCase()}`,
            JSON.stringify(invoiceData),
            JSON.stringify(response),
            'SUCCESS',
            apiStatus
        );

        // Record metrics
        if (processMonitor) {
            const responseTime = Date.now() - startTime;
            processMonitor.recordRequest(responseTime);
        }

        console.log(chalk.green(`[Get Invoice ${retrievalType}] Request ${requestId} completed successfully in ${Date.now() - startTime}ms`));

        return res.status(200).json(response);

    } catch (error) {
        console.error(chalk.red(`[Get Invoice ${retrievalType}] Unexpected error for request ${requestId}:`), error);

        // Record error metrics
        if (processMonitor) {
            processMonitor.recordError(error);
        }

        // Log unexpected error
        try {
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                `get_invoice_by_${retrievalType.toLowerCase()}`,
                JSON.stringify(invoiceData),
                JSON.stringify({ error: error.message, stack: error.stack }),
                'UNEXPECTED_ERROR',
                0
            );
        } catch (logError) {
            console.error(chalk.red(`[Get Invoice ${retrievalType}] Failed to log error for request ${requestId}:`), logError);
        }

        return res.status(500).json({
            success: false,
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
            requestId,
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * Handle unexpected errors
 */
const handleUnexpectedError = async (req, res, error, requestId, operation) => {
    console.error(chalk.red(`[Get Invoice] Unexpected error for request ${requestId}:`), error);

    // Record error metrics
    if (processMonitor) {
        processMonitor.recordError(error);
    }

    // Log unexpected error
    try {
        await logs.createApiLogs(
            database,
            req.user?.uid || 'anonymous',
            operation,
            JSON.stringify(req.body),
            JSON.stringify({ error: error.message, stack: error.stack }),
            'UNEXPECTED_ERROR',
            0
        );
    } catch (logError) {
        console.error(chalk.red(`[Get Invoice] Failed to log error for request ${requestId}:`), logError);
    }

    return res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
        requestId,
        timestamp: new Date().toISOString()
    });
};

// ============================================================================
// MODULE EXPORTS
// ============================================================================

module.exports = {
    getInvoiceByIrnClt,
    getInvoiceByDocumentClt,
    getInvoiceByIrnValidationSchema,
    getInvoiceByDocumentValidationSchema
};