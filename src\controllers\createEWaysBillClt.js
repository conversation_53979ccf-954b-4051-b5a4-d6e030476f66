// ============================================================================
// E-Way Bill Controller with Country-wise Processing
// ============================================================================
// This controller handles E-Way Bill creation with:
// - Country-specific processing logic (similar to createInvoiceClt)
// - Company validation and lookup
// - Comprehensive input validation
// - Enhanced error handling and logging
// - Request tracking and monitoring
// ============================================================================

const gspController = require('./sub-controllers/gspController');
const database = require('../model/DBClient');
const logs = require('../utils/utils');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const { ProcessMonitor } = require('../utils/processMonitor');

// Get process monitor instance
const processMonitor = ProcessMonitor ? new ProcessMonitor() : null;

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

// Enhanced E-Way Bill validation schema with comprehensive validation
const createEWaysBillValidationSchema = Joi.object({
    ewaysBillData: Joi.object({
        // Invoice Reference Number (IRN) - required for E-Way Bill generation
        controlCode: Joi.string().required().length(64).messages({
            'string.length': 'Control code (IRN) must be exactly 64 characters',
            'string.empty': 'Control code (IRN) is required for E-Way Bill generation'
        }),

        // Document details
        voucherNumber: Joi.string().required().min(1).max(50).messages({
            'string.empty': 'Voucher number is required',
            'string.max': 'Voucher number cannot exceed 50 characters'
        }),
        voucherTime: Joi.string().required().messages({
            'string.empty': 'Voucher time is required'
        }),

        // Transportation details
        vehicleNumber: Joi.string().required().min(1).max(20).pattern(/^[A-Z]{2}[0-9]{1,2}[A-Z]{1,2}[0-9]{4}$/).messages({
            'string.pattern.base': 'Vehicle number must be in valid Indian format (e.g., KA01AB1234)',
            'string.empty': 'Vehicle number is required',
            'string.max': 'Vehicle number cannot exceed 20 characters'
        }),

        // Distance in kilometers
        distance: Joi.number().min(1).max(4000).required().messages({
            'number.min': 'Distance must be at least 1 km',
            'number.max': 'Distance cannot exceed 4000 km',
            'any.required': 'Distance is required for E-Way Bill'
        }),

        // Vehicle type validation
        vehicleType: Joi.string().required().valid('REGULAR', 'ODC').messages({
            'any.only': 'Vehicle type must be either REGULAR or ODC (Over Dimensional Cargo)',
            'string.empty': 'Vehicle type is required'
        }),

        // Transporter details
        transportName: Joi.string().required().min(1).max(100).messages({
            'string.empty': 'Transport name is required',
            'string.max': 'Transport name cannot exceed 100 characters'
        }),

        // Optional transporter ID (GSTIN of transporter)
        transporterId: Joi.string().optional().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
            'string.pattern.base': 'Transporter ID must be a valid GSTIN format'
        }),

        // Transportation mode
        transportMode: Joi.string().optional().valid('1', '2', '3', '4').default('1').messages({
            'any.only': 'Transport mode must be: 1=Road, 2=Rail, 3=Air, 4=Ship'
        }),

        // Document type for transportation
        docType: Joi.string().optional().valid('INV', 'BIL', 'BOE', 'CHL', 'CNT', 'OTH').default('INV').messages({
            'any.only': 'Document type must be: INV=Invoice, BIL=Bill of Supply, BOE=Bill of Entry, CHL=Challan, CNT=Credit Note, OTH=Others'
        }),

        // Optional tracking ID
        uid: Joi.string().uuid().optional()
    }).required()
});

// ============================================================================
// MAIN CONTROLLER FUNCTION
// ============================================================================

const createEWaysBillClt = async (req, res) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || uuidv4();

    try {
        // ========================================================================
        // STEP 1: REQUEST LOGGING AND VALIDATION
        // ========================================================================

        console.log(chalk.blue(`[E-Way Bill] Request ${requestId} started - IP: ${req.ip}`));

        // Validate input data
        const { error, value } = createEWaysBillValidationSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            console.warn(chalk.yellow(`[E-Way Bill] Validation failed for request ${requestId}:`, validationErrors));

            // Log validation failure
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                '/eway/create',
                JSON.stringify(req.body),
                JSON.stringify({ error: 'Validation failed', details: validationErrors }),
                'VALIDATION_ERROR',
                0
            );

            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: validationErrors,
                requestId
            });
        }

        const { ewaysBillData } = value;

        // Add request ID to E-Way Bill data for tracking
        ewaysBillData.uid = ewaysBillData.uid || requestId;

        // ========================================================================
        // STEP 2: COMPANY VALIDATION AND LOOKUP
        // ========================================================================

        // Get company from authenticated request (should be set by auth middleware)
        let company = req.company;

        // Fallback to header-based lookup (for backward compatibility)
        if (!company) {
            const companyUid = req.headers['company-uid'];

            if (!companyUid) {
                console.warn(chalk.yellow(`[E-Way Bill] No company UID provided for request ${requestId}`));
                return res.status(400).json({
                    success: false,
                    error: 'Company UID is required',
                    requestId
                });
            }

            company = await database.CompanyInfo.findOne({
                attributes: ['companyUid', 'country_name', 'country_code'],
                raw: true,
                where: { companyUid: companyUid }
            });
        }

        if (!company) {
            console.warn(chalk.yellow(`[E-Way Bill] Company not found for request ${requestId}`));

            // Log company not found
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                '/eway/create',
                JSON.stringify({ companyUid: req.headers['company-uid'] }),
                JSON.stringify({ error: 'Company not found' }),
                'COMPANY_NOT_FOUND',
                0
            );

            return res.status(404).json({
                success: false,
                error: 'Company not found',
                requestId
            });
        }

        // ========================================================================
        // STEP 3: COUNTRY-WISE PROCESSING
        // ========================================================================

        let response;
        let apiStatus = 0; // Success by default

        switch (company.country_code) {
            case 'IND':
                try {
                    console.log(chalk.blue(`[E-Way Bill] Processing Indian E-Way Bill for request ${requestId}`));

                    const gspResponse = await gspController.createGSPEWayBill(ewaysBillData);

                    if (gspResponse.success === false || gspResponse.error) {
                        console.error(chalk.red(`[E-Way Bill] GSP E-Way Bill creation failed for request ${requestId}:`, gspResponse.error));
                        apiStatus = 1;

                        // Log GSP error
                        await logs.createApiLogs(
                            database,
                            req.user?.uid || 'anonymous',
                            '/ewaybill',
                            JSON.stringify(ewaysBillData),
                            JSON.stringify(gspResponse),
                            'GSP E-WAY BILL CREATION',
                            "GSP",
                            apiStatus
                        );

                        return res.status(500).json({
                            success: false,
                            error: 'Failed to create GSP E-Way Bill',
                            details: process.env.NODE_ENV === 'development' ? gspResponse.error : 'Internal processing error',
                            requestId
                        });
                    } else {
                        await logs.createApiLogs(
                            database,
                            req.user?.uid || 'anonymous',
                            '/ewaybill',
                            JSON.stringify(ewaysBillData),
                            JSON.stringify(gspResponse),
                            'GSP E-WAY BILL CREATION',
                            "GSP",
                            apiStatus
                        );
                    }

                    response = {
                        success: true,
                        message: 'GSP E-Way Bill created successfully',
                        data: gspResponse.data || gspResponse,
                        requestId,
                        timestamp: new Date().toISOString()
                    };

                    console.log(chalk.green(`[E-Way Bill] GSP E-Way Bill created successfully for request ${requestId}`));

                } catch (gspError) {
                    console.error(chalk.red(`[E-Way Bill] GSP processing error for request ${requestId}:`, gspError));
                    apiStatus = 0;

                    // Log GSP processing error
                    await logs.createApiLogs(
                        database,
                        req.user?.uid || 'anonymous',
                        'create_gsp_eway_bill',
                        JSON.stringify(ewaysBillData),
                        JSON.stringify({ error: gspError.message }),
                        'GSP_PROCESSING_ERROR',
                            
                    );

                    return res.status(500).json({
                        success: false,
                        error: 'GSP processing failed',
                        details: process.env.NODE_ENV === 'development' ? gspError.message : 'Internal processing error',
                        requestId
                    });
                }
                break;

            default:
                console.warn(chalk.yellow(`[E-Way Bill] Unsupported country code ${company.country_code} for request ${requestId}`));
                apiStatus = 1 ;

                // Log unsupported country
                await logs.createApiLogs(
                    database,
                    req.user?.uid || 'anonymous',
                    '/eway/create',
                    JSON.stringify({ companyUid: company.companyUid, countryCode: company.country_code }),
                    JSON.stringify({ error: 'Unsupported country code' }),
                    'UNSUPPORTED_COUNTRY',
                      1  
                );

                return res.status(400).json({
                    success: false,
                    error: 'Unsupported country code for E-Way Bill creation',
                    details: 'E-Way Bill is currently only supported for Indian companies',
                    supportedCountries: ['IND'],
                    currentCountry: company.country_code,
                    requestId
                });
        }

       // Record metrics
        if (processMonitor) {
            const responseTime = Date.now() - startTime;
            processMonitor.recordRequest(responseTime);
        }

        console.log(chalk.green(`[E-Way Bill] Request ${requestId} completed successfully in ${Date.now() - startTime}ms`));

        return res.status(200).json(response);
    }
    catch (error) {
        console.error(chalk.red(`[E-Way Bill] Unexpected error for request ${requestId}:`), error);

        // Record error metrics
        if (processMonitor) {
            processMonitor.recordError(error);
        }

        // Log unexpected error
        try {
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                '/eway/create',
                JSON.stringify(req.body),
                JSON.stringify({ error: error.message, stack: error.stack }),
                'UNEXPECTED_ERROR',
                1
            );
        }
        catch (logError) {
            console.error(chalk.red(`[E-Way Bill] Failed to log error for request ${requestId}:`), logError);
        }

        return res.status(500).json({
            success: false,
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
            requestId,
            timestamp: new Date().toISOString()
        });
    }
}

module.exports = {
    createEWaysBillClt
};











