const gspController = require('./sub-controllers/gspController');
const database = require('../model/DBClient');
const logs = require('../utils/utils');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const { ProcessMonitor } = require('../utils/processMonitor');

// Get process monitor instance
const processMonitor = ProcessMonitor ? new ProcessMonitor() : null;

const createEWaysBillValidationSchema = Joi.object({
    ewaysBillData: Joi.object({
        controlCode: Joi.string().required().length(64).messages({
            'string.length': 'Control code must be exactly 64 characters'
        }),
        voucherNumber: Joi.string().required().min(1).max(50),
        voucherTime: Joi.string().required(),
        vehicleNumber: Joi.string().required().min(1).max(50),
        distance: Joi.number().min(0).optional(),
        vehicleType: Joi.string().required().min(1).max(50),
        transportName: Joi.string().required().min(1).max(50),
        uid: Joi.string().uuid().optional()
    }).required()
});

const createEWaysBillClt = async (req, res) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || uuidv4();

    try {
        // Log request start
        console.log(chalk.blue(`[E-Way Bill] Request ${requestId} started - IP: ${req.ip}`));

        // Validate input
        const { error, value } = createEWaysBillValidationSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            console.warn(chalk.yellow(`[E-Way Bill] Validation failed for request ${requestId}:`, validationErrors));

            // Log validation failure
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'create_eway_bill',
                JSON.stringify(req.body),
                JSON.stringify({ error: 'Validation failed', details: validationErrors }),
                'VALIDATION_ERROR',
                0
            );

            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors,
                requestId
            });
        }

        const ewaysBillData = value.ewaysBillData;
        ewaysBillData.uid = ewaysBillData.uid || uuidv4();

        let response;
        let apiStatus = 0; // Success by default
        try {
            const gspResponse = await gspController.createGSPEWayBill(ewaysBillData);

            if (gspResponse.error) {
                console.error(chalk.red(`[E-Way Bill] GSP E-Way Bill creation failed for request ${requestId}:`, gspResponse.error));
                apiStatus = 1;

                // Log GSP error
                await logs.createApiLogs(
                    database,
                    req.user?.uid,
                    'gsp_create_eway_bill',
                    JSON.stringify(ewaysBillData),
                    JSON.stringify(gspResponse),
                    'E-WAY BILL CREATION',
                    'GSP',
                    '/eway-bill/create',
                    apiStatus,
                );

                return res.status(500).json({
                    error: 'Failed to create GSP E-Way Bill',
                    details: process.env.NODE_ENV === 'development' ? gspResponse.error : 'Internal processing error',
                    requestId
                });
            } else {
                await logs.createApiLogs(
                    database,
                    req.user?.uid,
                    'gsp_create_eway_bill',
                    JSON.stringify(ewaysBillData),
                    JSON.stringify(gspResponse),
                    'E-WAY BILL CREATED',
                    'GSP',
                    '/eway-bill/create',
                    apiStatus,
                );
            }

            response = {
                message: 'GSP E-Way Bill created successfully',
                data: gspResponse,
                requestId,
                timestamp: new Date().toISOString()
            };

            console.log(chalk.green(`[E-Way Bill] GSP E-Way Bill created successfully for request ${requestId}`));

        } catch (gspError) {
            console.error(chalk.red(`[E-Way Bill] GSP processing error for request ${requestId}:`, gspError));
            apiStatus = 0;

            // Log GSP processing error
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'create_gsp_eway_bill',
                JSON.stringify(ewaysBillData),
                JSON.stringify({ error: gspError.message }),
                'GSP_PROCESSING_ERROR',
                'E-WAY BILL CREATION',
                'GSP',
                '/eway-bill/create',
                apiStatus,
            );

            return res.status(500).json({
                error: 'GSP processing failed',
                details: process.env.NODE_ENV === 'development' ? gspError.message : 'Internal processing error',
                requestId
            });
        }
        // Log successful operation
        await logs.createApiLogs(
            database,
            req.user?.uid || 'anonymous',
            'create_eway_bill',
            JSON.stringify(ewaysBillData),
            JSON.stringify(response),
            'SUCCESS',
            'E-WAY BILL CREATION',
            '',
            apiStatus,
        );

        // Record metrics
        if (processMonitor) {
            const responseTime = Date.now() - startTime;
            processMonitor.recordRequest(responseTime);
        }

        console.log(chalk.green(`[E-Way Bill] Request ${requestId} completed successfully in ${Date.now() - startTime}ms`));

        return res.status(200).json(response);
    }
    catch (error) {
        console.error(chalk.red(`[E-Way Bill] Unexpected error for request ${requestId}:`), error);

        // Record error metrics
        if (processMonitor) {
            processMonitor.recordError(error);
        }

        // Log unexpected error
        try {
            await logs.createApiLogs(
                database,
                req.user?.uid || 'anonymous',
                'create_eway_bill',
                JSON.stringify(req.body),
                JSON.stringify({ error: error.message, stack: error.stack }),
                'UNEXPECTED_ERROR',
                'E-WAY BILL CREATION',
                '',
                1
            );
        }
        catch (logError) {
            console.error(chalk.red(`[E-Way Bill] Failed to log error for request ${requestId}:`), logError);
        }

        return res.status(500).json({
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
            requestId,
            timestamp: new Date().toISOString()
        });
    }
}

module.exports = {
    createEWaysBillClt
};











