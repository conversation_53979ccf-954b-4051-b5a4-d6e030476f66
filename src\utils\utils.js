require('dotenv').config();

const chalk = require('chalk');
const crypto = require('crypto');

const env = process.env.HOST_ENV || 'development';
const config = require('../config/envConfig')[env];

const ALGORITHM = 'aes-256-cbc';
const HMAC_ALGO = 'sha256';

if (!config) {
    console.error(chalk.red(`[Fatal] Invalid or missing config for environment: ${env}`));
    process.exit(1);
}

/**
 * create the Logs 
 */
const createApiLogs = async (DB, userUid, apiName, apiRequest, apiResponse, apiType, apiStatus = 0) => {
    try {
        if (!DB?.ApiLogs) {
            throw new Error("Invalid DB instance or missing ApiLogs model.");
        }

        const apiLog = {
            uid: uuidv4(),
            api_name: apiName,
            api_request: apiRequest || null,
            api_response: apiResponse || null,
            api_type: apiType,
            api_status: apiStatus,
            staffUid: staffUid,
        };

        return await DB.ApiLogs.create(apiLog);
    } catch (error) {
        console.log(chalk.red(`[ERROR] Getting Error While Create API Logs \n ${error}`));
    }
};


function encryptData(plainData) {

    const basedToken = config.based_token;

    if (!basedToken || basedToken.length < 32) {
        throw new Error('Invalid based_token. It must be at least 32 characters long.');
    }

    const iv = crypto.randomBytes(16);
    const key = crypto.createHash('sha256').update(basedToken).digest();

    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    let encrypted = cipher.update(JSON.stringify(plainData), 'utf8', 'base64');
    encrypted += cipher.final('base64');

    const hmac = crypto.createHmac(HMAC_ALGO, basedToken)
        .update(encrypted)
        .digest('hex');

    return {
        payload: encrypted,
        iv: iv.toString('base64'),
        hmac: hmac,
    };
}

function decryptData(payload, iv, hmac) {

    const basedToken = config.based_token;

    if (!basedToken || !payload || !iv || !hmac) {
        throw new Error('Missing input for decryption.');
    }

    const expectedHmac = crypto.createHmac(HMAC_ALGO, basedToken)
        .update(payload)
        .digest('hex');

    if (expectedHmac !== hmac) {
        throw new Error('HMAC validation failed. Possible tampering.');
    }

    const key = crypto.createHash('sha256').update(basedToken).digest();
    const decipher = crypto.createDecipheriv(ALGORITHM, key, Buffer.from(iv, 'base64'));

    let decrypted = decipher.update(payload, 'base64', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
}


module.exports = {
    encryptData,
    decryptData,
    createApiLogs
}